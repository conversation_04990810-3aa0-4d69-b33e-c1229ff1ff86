#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查看器和管理工具
用于查看、搜索和管理存储在MySQL数据库中的文章数据
"""

import sys
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database_manager import DatabaseManager
    from config import get_database_config
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("💡 请确保所有相关文件都在正确位置")
    sys.exit(1)


class DatabaseViewer:
    """数据库查看器"""
    
    def __init__(self):
        """初始化数据库查看器"""
        try:
            self.db_manager = DatabaseManager()
            print("✅ 数据库连接成功")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("📊 数据库查看器 - 主菜单")
        print("="*60)
        print("1. 📰 查看文章列表")
        print("2. 🔍 搜索文章")
        print("3. 📄 查看文章详情")
        print("4. 📊 话题统计")
        print("5. 📈 处理日志")
        print("6. 🗓️ 按日期查看")
        print("7. 📋 数据库统计")
        print("8. 🔧 数据库管理")
        print("9. 📤 导出数据")
        print("0. 🚪 退出")
        print("="*60)
    
    def list_articles(self, limit: int = 10, offset: int = 0):
        """查看文章列表"""
        print(f"\n📰 最新文章列表 (显示 {limit} 条)")
        print("-" * 80)
        
        articles = self.db_manager.get_articles(limit=limit, offset=offset)
        
        if not articles:
            print("❌ 没有找到文章")
            return
        
        for i, article in enumerate(articles, 1):
            created_time = article['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"{i:2d}. ID:{article['id']:3d} | {created_time}")
            print(f"    📝 话题: {article['topic'][:50]}...")
            print(f"    📄 标题: {article.get('title', '无标题')[:50]}...")
            print(f"    📊 字数: {article['word_count']:,} | 新闻: {article['news_count']} 条")
            print(f"    🏷️ 状态: {article['status']}")
            print()
    
    def search_articles(self, keyword: str):
        """搜索文章"""
        print(f"\n🔍 搜索关键词: '{keyword}'")
        print("-" * 60)
        
        articles = self.db_manager.get_articles(limit=50, topic=keyword)
        
        if not articles:
            print("❌ 没有找到相关文章")
            return
        
        print(f"✅ 找到 {len(articles)} 篇相关文章:")
        for i, article in enumerate(articles, 1):
            created_time = article['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"{i:2d}. ID:{article['id']:3d} | {created_time}")
            print(f"    📝 话题: {article['topic'][:60]}...")
            print(f"    📊 字数: {article['word_count']:,} | 新闻: {article['news_count']} 条")
            print()
    
    def show_article_detail(self, article_id: int):
        """显示文章详情"""
        print(f"\n📄 文章详情 (ID: {article_id})")
        print("=" * 80)
        
        article = self.db_manager.get_article_detail(article_id)
        
        if not article:
            print("❌ 文章不存在")
            return
        
        print(f"📝 话题: {article['topic']}")
        print(f"📄 标题: {article.get('title', '无标题')}")
        print(f"📅 创建时间: {article['created_at']}")
        print(f"📅 更新时间: {article['updated_at']}")
        print(f"📊 字数: {article['word_count']:,}")
        print(f"📰 相关新闻: {article['news_count']} 条")
        print(f"🏷️ 状态: {article['status']}")
        
        if article.get('news_sources'):
            print(f"📡 新闻来源: {', '.join(article['news_sources'][:3])}...")
        
        if article.get('top_news_titles'):
            print(f"🔥 主要新闻:")
            for i, title in enumerate(article['top_news_titles'][:3], 1):
                print(f"    {i}. {title[:60]}...")
        
        print("\n" + "-" * 60)
        print("📋 新闻要点总结:")
        print("-" * 60)
        if article.get('news_points'):
            print(article['news_points'][:500] + "..." if len(article['news_points']) > 500 else article['news_points'])
        else:
            print("无要点总结")
        
        print("\n" + "-" * 60)
        print("📰 文章内容:")
        print("-" * 60)
        content = article.get('content', '无内容')
        if len(content) > 1000:
            print(content[:1000] + "\n\n... (内容过长，已截断)")
        else:
            print(content)
    
    def show_topic_stats(self):
        """显示话题统计"""
        print("\n📊 话题统计")
        print("-" * 60)
        
        topics = self.db_manager.get_topic_stats(limit=20)
        
        if not topics:
            print("❌ 没有话题统计数据")
            return
        
        print(f"{'排名':<4} {'话题名称':<30} {'文章数':<8} {'新闻数':<8} {'最后更新'}")
        print("-" * 70)
        
        for i, topic in enumerate(topics, 1):
            last_updated = topic['last_updated'].strftime('%m-%d %H:%M')
            topic_name = topic['topic_name'][:28] + "..." if len(topic['topic_name']) > 30 else topic['topic_name']
            print(f"{i:<4} {topic_name:<30} {topic['article_count']:<8} {topic['total_news_count']:<8} {last_updated}")
    
    def show_processing_logs(self):
        """显示处理日志"""
        print("\n📈 处理日志")
        print("-" * 80)
        
        logs = self.db_manager.get_processing_logs(limit=10)
        
        if not logs:
            print("❌ 没有处理日志")
            return
        
        for log in logs:
            created_time = log['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            completed_time = log['completed_at'].strftime('%Y-%m-%d %H:%M:%S') if log['completed_at'] else '未完成'
            
            print(f"📦 批次: {log['batch_id']}")
            print(f"   📊 数据: {log['total_news']} 条新闻 → {log['total_topics']} 个话题 → {log['total_articles']} 篇文章")
            print(f"   📈 压缩比: {log.get('compression_ratio', '未知')}")
            print(f"   ⏱️ 耗时: {log.get('processing_time_seconds', 0)} 秒")
            print(f"   🏷️ 状态: {log['status']}")
            print(f"   📅 时间: {created_time} → {completed_time}")
            print()
    
    def show_articles_by_date(self, days_ago: int = 7):
        """按日期查看文章"""
        start_date = datetime.now() - timedelta(days=days_ago)
        
        print(f"\n🗓️ 最近 {days_ago} 天的文章")
        print("-" * 60)
        
        # 这里需要扩展数据库管理器的功能
        articles = self.db_manager.get_articles(limit=100)
        recent_articles = [a for a in articles if a['created_at'] >= start_date]
        
        if not recent_articles:
            print(f"❌ 最近 {days_ago} 天没有文章")
            return
        
        # 按日期分组
        by_date = {}
        for article in recent_articles:
            date_key = article['created_at'].strftime('%Y-%m-%d')
            if date_key not in by_date:
                by_date[date_key] = []
            by_date[date_key].append(article)
        
        for date_key in sorted(by_date.keys(), reverse=True):
            articles_on_date = by_date[date_key]
            print(f"📅 {date_key} ({len(articles_on_date)} 篇)")
            for article in articles_on_date:
                print(f"   • ID:{article['id']} {article['topic'][:40]}... ({article['word_count']:,}字)")
            print()
    
    def show_database_stats(self):
        """显示数据库统计"""
        print("\n📋 数据库统计")
        print("-" * 60)
        
        try:
            # 获取文章总数
            articles = self.db_manager.get_articles(limit=1000)  # 获取更多数据用于统计
            total_articles = len(articles)
            
            if total_articles == 0:
                print("❌ 数据库中没有文章")
                return
            
            # 统计字数
            total_words = sum(a['word_count'] for a in articles)
            avg_words = total_words / total_articles if total_articles > 0 else 0
            
            # 统计新闻数
            total_news = sum(a['news_count'] for a in articles)
            avg_news = total_news / total_articles if total_articles > 0 else 0
            
            # 按状态统计
            status_count = {}
            for article in articles:
                status = article['status']
                status_count[status] = status_count.get(status, 0) + 1
            
            # 最新和最旧文章
            if articles:
                newest = max(articles, key=lambda x: x['created_at'])
                oldest = min(articles, key=lambda x: x['created_at'])
            
            print(f"📊 总体统计:")
            print(f"   📰 文章总数: {total_articles:,} 篇")
            print(f"   📝 总字数: {total_words:,} 字")
            print(f"   📊 平均字数: {avg_words:,.0f} 字/篇")
            print(f"   📡 总新闻数: {total_news:,} 条")
            print(f"   📈 平均新闻数: {avg_news:.1f} 条/篇")
            
            print(f"\n🏷️ 状态分布:")
            for status, count in status_count.items():
                percentage = count / total_articles * 100
                print(f"   {status}: {count} 篇 ({percentage:.1f}%)")
            
            if articles:
                print(f"\n📅 时间范围:")
                print(f"   最新文章: {newest['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"   最旧文章: {oldest['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 计算时间跨度
                time_span = newest['created_at'] - oldest['created_at']
                print(f"   时间跨度: {time_span.days} 天")
        
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
    
    def run(self):
        """运行交互式界面"""
        print("🎯 欢迎使用数据库查看器！")
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\n请选择操作 (0-9): ").strip()
                
                if choice == '0':
                    print("👋 再见！")
                    break
                elif choice == '1':
                    limit = input("显示多少条文章？(默认10): ").strip()
                    limit = int(limit) if limit.isdigit() else 10
                    self.list_articles(limit=limit)
                elif choice == '2':
                    keyword = input("请输入搜索关键词: ").strip()
                    if keyword:
                        self.search_articles(keyword)
                elif choice == '3':
                    article_id = input("请输入文章ID: ").strip()
                    if article_id.isdigit():
                        self.show_article_detail(int(article_id))
                elif choice == '4':
                    self.show_topic_stats()
                elif choice == '5':
                    self.show_processing_logs()
                elif choice == '6':
                    days = input("查看最近多少天的文章？(默认7天): ").strip()
                    days = int(days) if days.isdigit() else 7
                    self.show_articles_by_date(days)
                elif choice == '7':
                    self.show_database_stats()
                elif choice == '8':
                    self.database_management()
                elif choice == '9':
                    self.export_data()
                else:
                    print("❌ 无效选择，请重新输入")
                
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")
                input("按回车键继续...")
    
    def database_management(self):
        """数据库管理功能"""
        print("\n🔧 数据库管理")
        print("-" * 40)
        print("1. 🧹 清理测试数据")
        print("2. 📊 重建索引")
        print("3. 🔄 数据库状态检查")
        print("0. 返回主菜单")
        
        choice = input("请选择操作: ").strip()
        
        if choice == '1':
            confirm = input("⚠️ 确定要清理测试数据吗？(y/N): ").strip().lower()
            if confirm == 'y':
                # 这里可以添加清理测试数据的逻辑
                print("🧹 清理测试数据功能待实现")
        elif choice == '3':
            print("🔄 检查数据库连接状态...")
            if self.db_manager.connection and self.db_manager.connection.is_connected():
                print("✅ 数据库连接正常")
            else:
                print("❌ 数据库连接异常")
    
    def export_data(self):
        """导出数据功能"""
        print("\n📤 数据导出")
        print("-" * 40)
        print("1. 📄 导出文章列表 (CSV)")
        print("2. 📊 导出话题统计 (JSON)")
        print("3. 📈 导出处理日志 (JSON)")
        print("0. 返回主菜单")
        
        choice = input("请选择导出类型: ").strip()
        
        if choice == '1':
            print("📄 导出文章列表功能待实现")
        elif choice == '2':
            print("📊 导出话题统计功能待实现")
        elif choice == '3':
            print("📈 导出处理日志功能待实现")
    
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if hasattr(self, 'db_manager') and self.db_manager:
            self.db_manager.close()


def main():
    """主函数"""
    try:
        viewer = DatabaseViewer()
        viewer.run()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
    finally:
        print("🔚 程序结束")


if __name__ == "__main__":
    main()
