#!/bin/bash

# 设置cron定时任务脚本

echo "🕐 设置新闻处理定时任务..."

# 获取当前脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "📁 脚本目录: $SCRIPT_DIR"

# 设置脚本权限
chmod +x "$SCRIPT_DIR/run_scheduled.sh"
chmod +x "$SCRIPT_DIR/scheduled_runner.py"

echo "✅ 脚本权限设置完成"

# 创建cron任务 - 直接运行主脚本
CRON_JOB="0 */6 * * * cd $SCRIPT_DIR && python3 automated_news_pipeline.py all >> $SCRIPT_DIR/logs/cron.log 2>&1"

echo "📝 准备添加cron任务:"
echo "   $CRON_JOB"

# 检查是否已存在相同任务
if crontab -l 2>/dev/null | grep -q "automated_news_pipeline.py"; then
    echo "⚠️ 发现已存在的定时任务，将先删除..."
    crontab -l 2>/dev/null | grep -v "automated_news_pipeline.py" | crontab -
fi

# 添加新的cron任务
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

echo "✅ Cron任务添加成功！"
echo ""
echo "📋 当前cron任务列表:"
crontab -l

echo ""
echo "🕐 定时任务说明:"
echo "   - 每6小时执行一次 (0:00, 6:00, 12:00, 18:00)"
echo "   - 日志保存在: $SCRIPT_DIR/logs/"
echo "   - 执行状态保存在: $SCRIPT_DIR/last_execution_status.json"
echo ""
echo "🛠️ 管理命令:"
echo "   查看cron任务: crontab -l"
echo "   删除cron任务: crontab -e (手动删除对应行)"
echo "   查看日志: tail -f $SCRIPT_DIR/logs/cron.log"
echo "   手动执行: cd $SCRIPT_DIR && python3 automated_news_pipeline.py all"
