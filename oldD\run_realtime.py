#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时运行新闻处理流水线（无缓冲版本）
"""

import os
import sys
import subprocess

def run_realtime():
    """实时运行新闻处理流水线"""
    print("🚀 实时运行新闻处理流水线")
    print("=" * 50)
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 构建命令
    script_path = os.path.join(script_dir, "automated_news_pipeline.py")
    cmd = [sys.executable, script_path, "all"]
    
    print(f"📝 执行命令: {' '.join(cmd)}")
    print(f"📁 工作目录: {script_dir}")
    print("🔄 开始执行（实时输出）...")
    print("-" * 50)
    
    try:
        # 直接执行，无缓冲
        result = subprocess.run(
            cmd,
            cwd=script_dir,
            env=os.environ.copy()
        )
        
        print("-" * 50)
        if result.returncode == 0:
            print("✅ 执行成功完成")
        else:
            print(f"❌ 执行失败，返回码: {result.returncode}")
            
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n⏸️ 用户中断执行")
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

if __name__ == "__main__":
    success = run_realtime()
    sys.exit(0 if success else 1)
