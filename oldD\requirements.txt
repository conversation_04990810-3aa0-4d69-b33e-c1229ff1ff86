# 核心依赖
requests>=2.31.0
pandas>=2.0.0
numpy>=1.24.0
python-dotenv>=1.0.0

# 网页爬取相关
beautifulsoup4>=4.12.0
lxml>=4.9.0

# 向量数据库
chromadb>=0.4.0

# 数据库连接（选择其中一个）
mysql-connector-python>=8.0.0
# 或者使用 PyMySQL 作为备选：
# PyMySQL>=1.0.0

# 并发处理（Python 3.2+内置）
# concurrent.futures  # Python 3.2+已内置，无需安装

# 邮件发送
# smtplib  # Python内置
# email    # Python内置

# 数据处理
# json     # Python内置
# csv      # Python内置
# re       # Python内置
# threading # Python内置
# time     # Python内置
# os       # Python内置
# sys      # Python内置
# datetime # Python内置
# typing   # Python内置
# queue    # Python内置
# random   # Python内置
# glob     # Python内置
# shutil   # Python内置
