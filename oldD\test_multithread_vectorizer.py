#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多线程向量化功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from csv_to_vector_processor import CSVNewsVectorProcessor
    from config import get_embedding_config
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_multithread_vectorizer():
    """测试多线程向量化功能"""
    print("🧪 测试多线程向量化功能")
    print("=" * 50)
    
    # 创建测试数据
    test_news_data = [
        {
            'id': 1,
            'title': '测试新闻1',
            'content': '这是第一条测试新闻的内容，用于验证多线程向量化功能是否正常工作。',
            'media': '测试媒体',
            'category': '测试分类',
            'url': 'http://test1.com',
            'create_date': '2025-06-30',
            'create_time': '12:00:00',
            'crawl_time': '2025-06-30 12:00:00'
        },
        {
            'id': 2,
            'title': '测试新闻2',
            'content': '这是第二条测试新闻的内容，同样用于验证多线程向量化功能。',
            'media': '测试媒体',
            'category': '测试分类',
            'url': 'http://test2.com',
            'create_date': '2025-06-30',
            'create_time': '12:01:00',
            'crawl_time': '2025-06-30 12:01:00'
        },
        {
            'id': 3,
            'title': '测试新闻3',
            'content': '这是第三条测试新闻的内容，继续测试多线程处理能力。',
            'media': '测试媒体',
            'category': '测试分类',
            'url': 'http://test3.com',
            'create_date': '2025-06-30',
            'create_time': '12:02:00',
            'crawl_time': '2025-06-30 12:02:00'
        },
        {
            'id': 4,
            'title': '测试新闻4',
            'content': '这是第四条测试新闻的内容，用于测试批次处理功能。',
            'media': '测试媒体',
            'category': '测试分类',
            'url': 'http://test4.com',
            'create_date': '2025-06-30',
            'create_time': '12:03:00',
            'crawl_time': '2025-06-30 12:03:00'
        },
        {
            'id': 5,
            'title': '测试新闻5',
            'content': '这是第五条测试新闻的内容，最后一条测试数据。',
            'media': '测试媒体',
            'category': '测试分类',
            'url': 'http://test5.com',
            'create_date': '2025-06-30',
            'create_time': '12:04:00',
            'crawl_time': '2025-06-30 12:04:00'
        }
    ]
    
    try:
        # 获取配置
        embedding_config = get_embedding_config()
        
        # 创建处理器
        processor = CSVNewsVectorProcessor(embedding_config, "test_multithread_vectors")
        
        print(f"📊 测试数据: {len(test_news_data)} 条新闻")
        print(f"🔧 配置: 批次大小=2, 线程数=2")
        
        # 测试多线程处理
        processor.process_news_data(
            news_data=test_news_data,
            batch_size=2,      # 小批次，便于观察多线程效果
            max_news=None,     # 处理全部
            max_workers=2      # 2个线程
        )
        
        print("\n📊 处理结果:")
        print(f"   总处理数: {processor.stats['total_processed']}")
        print(f"   成功向量化: {processor.stats['successful_vectorized']}")
        print(f"   失败数: {processor.stats['failed_vectorized']}")
        print(f"   跳过数: {processor.stats['skipped_low_quality']}")
        
        if processor.stats['successful_vectorized'] > 0:
            print("✅ 多线程向量化测试成功！")
            return True
        else:
            print("❌ 多线程向量化测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_multithread_vectorizer()
