"""
CSV新闻数据向量化处理器
将新浪爬取的CSV新闻数据转换为向量并存储到向量数据库
"""

import pandas as pd
import requests
import json
import time
import re
import sys
import os
from typing import List, Dict, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from vector_database import SimpleVectorDB
    from config import get_embedding_config
except ImportError:
    print("❌ 找不到 vector_database.py 或 config.py 文件")
    print("💡 请确保相关文件在当前目录")
    sys.exit(1)


class CSVNewsVectorProcessor:
    """CSV新闻向量化处理器"""
    
    def __init__(self, embedding_config, vector_db_path="csv_news_vectors"):
        self.embedding_config = embedding_config
        self.vector_db = SimpleVectorDB(vector_db_path)
        self.session = requests.Session()
        # 不在这里设置headers，在每次请求时单独设置，确保线程安全
        self.last_request_time = 0
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'successful_vectorized': 0,
            'failed_vectorized': 0,
            'skipped_low_quality': 0
        }

        # 线程安全锁
        self.stats_lock = threading.Lock()
        self.request_lock = threading.Lock()
    
    def load_csv_data(self, csv_file_path: str) -> pd.DataFrame:
        """加载CSV新闻数据"""
        print(f"📂 加载CSV文件: {csv_file_path}")
        
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file_path, encoding='utf-8')
            
            print(f"✅ 成功加载 {len(df)} 条新闻记录")
            print(f"📊 数据列: {list(df.columns)}")
            
            # 显示数据概览
            print(f"\n📋 数据概览:")
            print(f"   - 标题示例: {df['title'].iloc[0][:50]}...")
            print(f"   - 媒体来源: {df['media'].value_counts().head(3).to_dict()}")
            print(f"   - 分类分布: {df['category'].value_counts().head(3).to_dict()}")
            
            return df
            
        except Exception as e:
            print(f"❌ 加载CSV文件失败: {e}")
            return pd.DataFrame()
    
    def clean_content(self, content: str) -> str:
        """清洗新闻内容"""
        if pd.isna(content) or not content:
            return ""
        
        # 转换为字符串
        content = str(content)
        
        # 移除多余的换行和空格
        content = re.sub(r'\n+', '\n', content)
        content = re.sub(r'\s+', ' ', content)
        
        # 移除特殊字符
        content = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,!?;:()（）"""]', '', content)
        
        # 移除过短的句子
        sentences = content.split('。')
        valid_sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        return '。'.join(valid_sentences).strip()
    
    def evaluate_content_quality(self, title: str, content: str) -> float:
        """评估内容质量"""
        score = 0.0
        
        # 标题质量 (0-0.3)
        if title and len(title) > 5:
            score += 0.1
        if title and len(title) > 15:
            score += 0.1
        if title and not any(char in title for char in ['？', '！', '…']):
            score += 0.1
        
        # 内容长度 (0-0.4)
        if len(content) > 100:
            score += 0.1
        if len(content) > 300:
            score += 0.1
        if len(content) > 800:
            score += 0.1
        if len(content) > 1500:
            score += 0.1
        
        # 内容结构 (0-0.3)
        if '。' in content:
            score += 0.1
        if content.count('。') >= 3:
            score += 0.1
        if any(keyword in content for keyword in ['报道', '消息', '据悉', '记者', '新华社', '中新网']):
            score += 0.1
        
        return min(score, 1.0)
    
    def _rate_limit_wait(self):
        """API限流控制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        min_interval = 60 / self.embedding_config.get('rate_limit', 60)
        
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            time.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    def get_embeddings(self, texts: List[str], thread_id: str = "") -> Optional[List[List[float]]]:
        """获取文本向量（线程安全）"""
        if len(texts) > 10:
            raise ValueError("API最多支持10个文本")

        # 使用锁确保API调用的线程安全
        with self.request_lock:
            self._rate_limit_wait()

            url = self.embedding_config['base_url'].format(
                project_id=self.embedding_config['project_id']
            )

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.embedding_config['api_key']}"
            }

            # 检查文本内容
            for i, text in enumerate(texts):
                if len(text) > 8000:  # 如果文本过长，截断
                    texts[i] = text[:8000] + "..."
                    print(f"   ⚠️ [{thread_id}] 文本 {i+1} 过长，已截断到8000字符")

            data = {
                "easyllm_id": self.embedding_config['easyllm_id'],
                "input_texts": texts,
                "dimensions": self.embedding_config['dimensions']
            }

            # 简化调试信息
            print(f"   📊 [{thread_id}] 发送 {len(texts)} 个文本进行向量化")

            try:
                response = self.session.post(url, headers=headers, json=data, timeout=30)
                response.raise_for_status()

                result = response.json()
                embeddings = [item['embedding'] for item in result['data']]

                print(f"   ✅ [{thread_id}] API调用成功，获得 {len(embeddings)} 个向量")
                return embeddings

            except requests.exceptions.HTTPError as e:
                print(f"   ❌ [{thread_id}] HTTP错误: {e}")
                print(f"   📄 响应状态码: {response.status_code}")
                print(f"   📄 响应内容: {response.text}")
                return None
            except Exception as e:
                print(f"   ❌ [{thread_id}] API请求失败: {e}")
                return None
    
    def process_news_batch(self, news_batch: List[Dict], batch_num: int, total_batches: int, thread_id: str = ""):
        """处理一批新闻（支持多线程）"""
        print(f"\n🔄 [{thread_id}] 处理第 {batch_num}/{total_batches} 批次 ({len(news_batch)} 条新闻)...")
        
        # 准备向量化的文本
        texts_for_embedding = []
        valid_news = []
        
        for news in news_batch:
            # 清洗内容
            cleaned_content = self.clean_content(news['content'])
            
            # 质量评估
            quality_score = self.evaluate_content_quality(news['title'], cleaned_content)
            
            # 只要有内容就处理，不设置质量过滤
            if len(cleaned_content) >= 20:  # 只要求最基本的内容长度
                # 准备向量化文本（标题+内容）
                text_for_embedding = f"{news['title']}\n{cleaned_content}"
                texts_for_embedding.append(text_for_embedding)

                # 保存处理后的新闻信息
                news['cleaned_content'] = cleaned_content
                news['quality_score'] = quality_score
                valid_news.append(news)
            else:
                print(f"⚠️ [{thread_id}] 跳过内容过短新闻: {news['title'][:30]}... (内容长度: {len(cleaned_content)})")
                with self.stats_lock:
                    self.stats['skipped_low_quality'] += 1
        
        if not valid_news:
            print(f"❌ [{thread_id}] 本批次没有符合质量要求的新闻")
            return

        print(f"📝 [{thread_id}] 本批次有效新闻: {len(valid_news)} 条")

        # 批量向量化
        embeddings = self.get_embeddings(texts_for_embedding, thread_id)

        if not embeddings:
            print(f"❌ [{thread_id}] 向量化失败")
            with self.stats_lock:
                self.stats['failed_vectorized'] += len(valid_news)
            return
        
        # 存储到向量数据库
        for news, embedding in zip(valid_news, embeddings):
            try:
                # 生成向量ID
                vector_id = f"sina_news_{news['id']}_{int(time.time())}"
                
                # 准备元数据
                metadata = {
                    'news_id': news['id'],
                    'title': news['title'],
                    'content': news['cleaned_content'],
                    'media': news['media'],
                    'category': news['category'],
                    'url': news['url'],
                    'publish_time': f"{news['create_date']} {news['create_time']}",
                    'quality_score': news['quality_score'],
                    'rank': news.get('rank', 0),
                    'top_num': news.get('top_num', ''),
                    'crawl_time': news['crawl_time'],
                    'processed_time': datetime.now().isoformat()
                }
                
                # 存储到向量数据库
                self.vector_db.add_document(vector_id, embedding, metadata)
                with self.stats_lock:
                    self.stats['successful_vectorized'] += 1

            except Exception as e:
                print(f"❌ [{thread_id}] 存储新闻失败: {e}")
                with self.stats_lock:
                    self.stats['failed_vectorized'] += 1
        
        print(f"✅ [{thread_id}] 第 {batch_num} 批次处理完成")
    
    def process_news_data(self, news_data: List[Dict], batch_size: int = 10, max_news: int = None, max_workers: int = 3):
        """处理新闻数据（直接处理内存中的数据，支持多线程）"""
        print(f"🚀 开始多线程处理新闻数据...")
        print(f"   数据条数: {len(news_data)}")
        print(f"   批次大小: {batch_size}")
        print(f"   最大处理数: {max_news or '全部'}")
        print(f"   线程数: {max_workers}")

        # 转换为DataFrame
        df = pd.DataFrame(news_data)

        # 调用通用处理方法
        return self._process_dataframe(df, batch_size, max_news, max_workers)

    def process_csv_file(self, csv_file_path: str, batch_size: int = 10, max_news: int = None, max_workers: int = 3):
        """处理整个CSV文件（保留兼容性，支持多线程）"""
        print(f"🚀 开始多线程处理CSV新闻文件...")
        print(f"   文件路径: {csv_file_path}")
        print(f"   批次大小: {batch_size}")
        print(f"   最大处理数: {max_news or '全部'}")
        print(f"   线程数: {max_workers}")

        # 加载数据
        df = self.load_csv_data(csv_file_path)

        # 调用通用处理方法
        return self._process_dataframe(df, batch_size, max_news, max_workers)

    def _process_dataframe(self, df: pd.DataFrame, batch_size: int = 10, max_news: int = None, max_workers: int = 3):
        """处理DataFrame数据（支持多线程）"""
        if df.empty:
            print("❌ 没有数据可处理")
            return

        # 限制处理数量
        if max_news:
            df = df.head(max_news)
            print(f"📝 限制处理前 {max_news} 条新闻")

        # 转换为字典列表
        news_list = df.to_dict('records')
        self.stats['total_processed'] = len(news_list)

        # 分批处理
        total_batches = (len(news_list) + batch_size - 1) // batch_size

        print(f"🚀 启动多线程向量化处理...")
        print(f"⚙️ 线程数: {max_workers}")
        print(f"⚙️ 批次数: {total_batches}")
        print(f"⚙️ 批次大小: {batch_size}")

        # 准备批次任务
        batch_tasks = []
        for i in range(0, len(news_list), batch_size):
            batch_news = news_list[i:i + batch_size]
            batch_num = i // batch_size + 1
            batch_tasks.append((batch_news, batch_num, total_batches))

        # 多线程处理批次
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_batch = {}
            for batch_news, batch_num, total_batches in batch_tasks:
                thread_id = f"T{batch_num:02d}"
                future = executor.submit(self.process_news_batch, batch_news, batch_num, total_batches, thread_id)
                future_to_batch[future] = batch_num

            # 等待所有任务完成
            completed = 0
            for future in as_completed(future_to_batch):
                batch_num = future_to_batch[future]
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                    completed += 1
                    print(f"🎯 进度: {completed}/{total_batches} 批次完成")
                except Exception as e:
                    print(f"❌ 批次 {batch_num} 处理失败: {e}")

        print(f"✅ 多线程处理完成！")

        # 保存向量数据库
        print(f"💾 保存向量数据库...")
        self.vector_db.save_database()
        
        # 显示最终统计
        self.print_final_stats()
    
    def print_final_stats(self):
        """打印最终统计信息"""
        print("\n" + "="*60)
        print("📊 处理完成统计")
        print("="*60)
        print(f"总处理数量: {self.stats['total_processed']}")
        print(f"成功向量化: {self.stats['successful_vectorized']}")
        print(f"向量化失败: {self.stats['failed_vectorized']}")
        print(f"跳过低质量: {self.stats['skipped_low_quality']}")
        
        if self.stats['total_processed'] > 0:
            success_rate = self.stats['successful_vectorized'] / self.stats['total_processed'] * 100
            print(f"成功率: {success_rate:.1f}%")
        
        # 向量数据库统计
        db_stats = self.vector_db.get_stats()
        print(f"\n向量数据库统计:")
        print(f"总文档数: {db_stats['total_documents']}")
        print(f"向量维度: {db_stats['vector_dimension']}")
        print(f"存储路径: {db_stats['storage_path']}")
    
    def test_similarity_search(self, query: str, top_k: int = 5):
        """测试相似度搜索"""
        print(f"\n🔍 测试相似度搜索: {query}")
        print("-" * 40)
        
        # 获取查询向量
        query_embedding = self.get_embeddings([query])
        
        if not query_embedding:
            print("❌ 获取查询向量失败")
            return
        
        # 搜索相似新闻
        results = self.vector_db.search(query_embedding[0], top_k, threshold=0.3)
        
        if results:
            print(f"✅ 找到 {len(results)} 条相关新闻:")
            for result in results:
                metadata = result['metadata']
                print(f"\n{result['rank']}. 相似度: {result['similarity']:.3f}")
                print(f"   📰 {metadata['title']}")
                print(f"   🏷️  {metadata['category']} | 📰 {metadata['media']}")
                print(f"   📅 {metadata['publish_time']}")
        else:
            print("❌ 未找到相关新闻")


# 从配置文件加载配置信息
EMBEDDING_CONFIG = get_embedding_config()


def main():
    """主函数"""
    print("CSV新闻向量化处理器")
    print("="*60)

    # 生成当天的时间戳
    from datetime import datetime
    today = datetime.now().strftime("%Y%m%d")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    print(f"处理日期: {today}")
    print(f"处理时间: {timestamp}")

    # 配置已经设置好了，直接开始处理
    print("使用配置:")
    print(f"   Project ID: {EMBEDDING_CONFIG['project_id']}")
    print(f"   EasyLLM ID: {EMBEDDING_CONFIG['easyllm_id']}")
    print(f"   API Key: {EMBEDDING_CONFIG['api_key'][:20]}...")

    # 创建当天专用的向量数据库路径
    vector_db_path = f"news_vectors_{today}"
    print(f"向量数据库路径: {vector_db_path}")
    print("每天使用独立的向量数据库，确保数据不复用")

    # 检查是否存在当天的数据库，如果存在则清理
    import shutil
    if os.path.exists(vector_db_path):
        print(f"发现当天已有数据库，清理旧数据...")
        shutil.rmtree(vector_db_path)
        print(f"旧数据库已清理，确保使用全新数据")

    # 自动查找当天最新的CSV文件
    import glob
    csv_pattern = f"新闻爬取/*news*{today}*.csv"
    csv_files = glob.glob(csv_pattern)

    if not csv_files:
        # 如果没有当天的文件，查找最新的文件
        csv_files = glob.glob("新闻爬取/*news*.csv")
        if csv_files:
            csv_file = max(csv_files, key=os.path.getctime)  # 最新创建的文件
            print(f"未找到当天文件，使用最新文件: {csv_file}")
        else:
            print("未找到任何CSV文件")
            return
    else:
        csv_file = max(csv_files, key=os.path.getctime)  # 当天最新的文件
        print(f"找到当天文件: {csv_file}")

    print(f"处理文件: {csv_file}")

    # 创建处理器（使用当天专用的数据库路径）
    processor = CSVNewsVectorProcessor(EMBEDDING_CONFIG, vector_db_path)

    # 处理CSV文件（处理全部新闻）
    processor.process_csv_file(
        csv_file_path=csv_file,
        batch_size=10,     # 合理的批次大小
        max_news=None      # 处理全部新闻
    )
    
    # 测试相似度搜索
    test_queries = [
        "印度拒签上合组织声明",
        "未成年人违法拘留新规",
        "治安管理处罚法修订"
    ]
    
    for query in test_queries:
        processor.test_similarity_search(query, top_k=3)
    
    print("\n🎉 处理完成！")


if __name__ == "__main__":
    main()
