#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时执行新闻处理流水线
包含完善的错误处理、日志记录和监控功能
"""

import os
import sys
import subprocess
import logging
import json
from datetime import datetime
import traceback
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class ScheduledNewsRunner:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.log_dir = os.path.join(self.script_dir, "logs")
        self.ensure_log_directory()
        self.setup_logging()
        
    def ensure_log_directory(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def setup_logging(self):
        """设置日志配置"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(self.log_dir, f"scheduled_news_{timestamp}.log")
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)

        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)

        # 设置格式
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            handlers=[file_handler, console_handler]
        )

        # 禁用缓冲
        file_handler.stream.reconfigure(line_buffering=True)
        console_handler.stream.reconfigure(line_buffering=True)
        self.logger = logging.getLogger(__name__)
        self.log_file = log_file
    
    def cleanup_old_logs(self, days=7):
        """清理旧日志文件"""
        try:
            current_time = time.time()
            for filename in os.listdir(self.log_dir):
                if filename.startswith("scheduled_news_") and filename.endswith(".log"):
                    file_path = os.path.join(self.log_dir, filename)
                    file_time = os.path.getmtime(file_path)
                    if current_time - file_time > days * 24 * 3600:
                        os.remove(file_path)
                        self.logger.info(f"🗑️ 删除旧日志文件: {filename}")
        except Exception as e:
            self.logger.warning(f"⚠️ 清理日志文件时出错: {e}")
    
    def check_environment(self):
        """检查运行环境"""
        self.logger.info("🔍 检查运行环境...")
        
        # 检查Python版本
        python_version = sys.version
        self.logger.info(f"🐍 Python版本: {python_version}")
        
        # 检查必要文件
        required_files = [
            "automated_news_pipeline.py",
            "config.py",
            ".env"
        ]
        
        for file in required_files:
            file_path = os.path.join(self.script_dir, file)
            if os.path.exists(file_path):
                self.logger.info(f"✅ 文件存在: {file}")
            else:
                self.logger.error(f"❌ 文件缺失: {file}")
                return False
        
        return True
    
    def run_news_pipeline(self):
        """执行新闻处理流水线"""
        self.logger.info("🚀 开始执行新闻处理流水线")
        
        try:
            # 确保切换到脚本目录
            original_dir = os.getcwd()
            os.chdir(self.script_dir)
            self.logger.info(f"当前工作目录: {os.getcwd()}")

            # 使用绝对路径执行主脚本
            script_path = os.path.join(self.script_dir, "automated_news_pipeline.py")
            cmd = [sys.executable, script_path, "all"]
            self.logger.info(f"📝 执行命令: {' '.join(cmd)}")
            self.logger.info(f"脚本路径: {script_path}")

            # 检查脚本文件是否存在
            if not os.path.exists(script_path):
                self.logger.error(f"脚本文件不存在: {script_path}")
                return False
            
            start_time = time.time()

            # 设置环境变量，确保子进程能找到模块
            env = os.environ.copy()
            env['PYTHONPATH'] = self.script_dir

            # 执行并捕获输出
            self.logger.info("正在启动新闻处理流水线...")
            self.logger.info(f"PYTHONPATH: {env.get('PYTHONPATH', 'Not set')}")

            # 使用Popen实现真正的实时输出
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 合并stderr到stdout
                text=True,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                bufsize=0,  # 无缓冲
                universal_newlines=True,
                env=env,
                cwd=self.script_dir
            )

            # 实时读取输出
            stdout_lines = []
            while True:
                try:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        line = output.strip()
                        stdout_lines.append(line)
                        self.logger.info(f"   {line}")
                        # 强制刷新日志
                        for handler in self.logger.handlers:
                            handler.flush()
                except UnicodeDecodeError as e:
                    self.logger.warning(f"编码错误，跳过: {e}")
                    continue
                except Exception as e:
                    self.logger.error(f"读取输出时出错: {e}")
                    break

            # 获取最终结果
            result_returncode = process.returncode
            stdout = '\n'.join(stdout_lines)
            stderr = ""
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录剩余输出
            if stdout:
                self.logger.info("📤 最终标准输出:")
                for line in stdout.split('\n'):
                    if line.strip():
                        self.logger.info(f"   {line}")

            if stderr:
                self.logger.warning("⚠️ 错误输出:")
                for line in stderr.split('\n'):
                    if line.strip():
                        self.logger.warning(f"   {line}")

            # 检查执行结果
            if result_returncode == 0:
                self.logger.info(f"✅ 新闻处理完成成功 (耗时: {execution_time:.2f}秒)")
                return True
            else:
                self.logger.error(f"❌ 新闻处理执行失败 (返回码: {result.returncode})")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error("⏰ 执行超时 (超过1小时)")
            return False
        except Exception as e:
            self.logger.error(f"💥 执行异常: {e}")
            self.logger.error(f"📋 异常详情: {traceback.format_exc()}")
            return False
    
    def save_execution_status(self, success: bool):
        """保存执行状态"""
        status_file = os.path.join(self.script_dir, "last_execution_status.json")
        status_data = {
            "timestamp": datetime.now().isoformat(),
            "success": success,
            "log_file": self.log_file
        }
        
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"💾 执行状态已保存: {status_file}")
        except Exception as e:
            self.logger.warning(f"⚠️ 保存执行状态失败: {e}")
    
    def run(self):
        """主执行函数"""
        self.logger.info("=" * 60)
        self.logger.info("🚀 定时新闻处理任务开始")
        self.logger.info(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info("=" * 60)
        
        try:
            # 检查环境
            if not self.check_environment():
                self.logger.error("❌ 环境检查失败，终止执行")
                self.save_execution_status(False)
                return False
            
            # 清理旧日志
            self.cleanup_old_logs()
            
            # 执行新闻处理
            success = self.run_news_pipeline()
            
            # 保存执行状态
            self.save_execution_status(success)
            
            self.logger.info("=" * 60)
            self.logger.info(f"🏁 定时任务完成: {'成功' if success else '失败'}")
            self.logger.info(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info("=" * 60)
            
            return success
            
        except Exception as e:
            self.logger.error(f"💥 定时任务异常: {e}")
            self.logger.error(f"📋 异常详情: {traceback.format_exc()}")
            self.save_execution_status(False)
            return False

if __name__ == "__main__":
    runner = ScheduledNewsRunner()
    success = runner.run()
    sys.exit(0 if success else 1)
