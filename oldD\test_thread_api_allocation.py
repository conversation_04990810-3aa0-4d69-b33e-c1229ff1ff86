#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试线程API key分配机制
"""

import sys
import os
import threading
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database_topic_merger import DatabaseTopicMerger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_thread_api_allocation():
    """测试线程API key分配"""
    print("🧪 测试线程API key分配机制")
    print("=" * 50)
    
    try:
        # 创建话题合并器
        merger = DatabaseTopicMerger()
        
        def thread_worker(thread_id: str):
            """线程工作函数"""
            print(f"🔄 线程 [{thread_id}] 开始工作")
            
            # 获取分配的API key
            api_key = merger.get_thread_api_key(thread_id)
            print(f"🔑 线程 [{thread_id}] 获得API Key: {api_key[:20]}...")
            
            # 模拟API调用
            result = merger.call_llm("你好，这是一个测试。请简单回复。", thread_id)
            
            if result:
                print(f"✅ 线程 [{thread_id}] API调用成功: {result[:50]}...")
            else:
                print(f"❌ 线程 [{thread_id}] API调用失败")
        
        # 创建多个线程测试
        threads = []
        thread_ids = ["T01", "T02", "T03", "T04", "T05"]
        
        print(f"🚀 启动 {len(thread_ids)} 个线程测试...")
        
        for thread_id in thread_ids:
            thread = threading.Thread(target=thread_worker, args=(thread_id,))
            threads.append(thread)
            thread.start()
            time.sleep(0.2)  # 错开启动时间
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print("\n" + "=" * 50)
        print("📊 线程API key分配结果:")
        for thread_id, api_key in merger.thread_api_keys.items():
            print(f"   线程 [{thread_id}]: {api_key[:20]}...")
        
        print(f"✅ 测试完成！共分配了 {len(merger.thread_api_keys)} 个线程专用API key")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_thread_api_allocation()
