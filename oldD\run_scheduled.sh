#!/bin/bash

# 自动化新闻处理定时脚本
# 每6小时执行一次

# 设置脚本目录
SCRIPT_DIR="/home/<USER>/oldDfuzaijunheng"
LOG_DIR="$SCRIPT_DIR/logs"
PYTHON_PATH="/usr/bin/python3"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 生成时间戳
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
LOG_FILE="$LOG_DIR/news_pipeline_$TIMESTAMP.log"

echo "========================================" >> "$LOG_FILE"
echo "🚀 开始执行新闻处理流水线" >> "$LOG_FILE"
echo "⏰ 执行时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$LOG_FILE"
echo "========================================" >> "$LOG_FILE"

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 执行新闻处理流水线
echo "📰 启动新闻处理..." >> "$LOG_FILE"
$PYTHON_PATH automated_news_pipeline.py all >> "$LOG_FILE" 2>&1

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "✅ 新闻处理完成成功" >> "$LOG_FILE"
    echo "📊 执行结果: 成功" >> "$LOG_FILE"
else
    echo "❌ 新闻处理执行失败" >> "$LOG_FILE"
    echo "📊 执行结果: 失败" >> "$LOG_FILE"
fi

echo "⏰ 结束时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$LOG_FILE"
echo "========================================" >> "$LOG_FILE"

# 保留最近7天的日志文件
find "$LOG_DIR" -name "news_pipeline_*.log" -mtime +7 -delete

# 可选：发送执行状态到监控系统
# curl -X POST "your-monitoring-webhook" -d "status=completed&timestamp=$(date)"

echo "定时任务执行完成，日志保存在: $LOG_FILE"
