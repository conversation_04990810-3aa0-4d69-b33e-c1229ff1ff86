"""
从向量数据库中提取所有新闻标题，发给LLM合并相同类似的话题
"""

import requests
import json
import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict
import queue
import random

# 添加向量数据库路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from vector_database import VectorDatabase
from config import get_llm_config, get_embedding_config, get_database_config
from database_manager import DatabaseManager


class DatabaseTopicMerger:
    """从数据库提取话题并合并，然后生成文章"""

    def __init__(self):
        # LLM API配置 - 从环境文件加载多个API key
        llm_config = get_llm_config()
        self.llm_config = {
            "api_keys": llm_config["api_keys"],
            "base_url": "https://www.sophnet.com/api/open-apis/chat/completions",
            "model": "DeepSeek-V3-Fast"
        }

        # 为每个线程预分配API key（避免竞争）
        self.api_keys = self.llm_config["api_keys"].copy()
        import random
        random.shuffle(self.api_keys)  # 随机打乱顺序

        # 线程到API key的映射
        self.thread_api_keys = {}

        print(f"🔑 API Key池初始化完成，共 {len(self.api_keys)} 个key（支持线程独立分配）")

        # Embedding API配置 - 从环境文件加载
        embedding_config = get_embedding_config()
        self.embedding_config = {
            "api_key": embedding_config["api_key"],
            "base_url": "https://www.sophnet.com/api/open-apis/projects/{project_id}/easyllms/embeddings",
            "project_id": embedding_config["project_id"],
            "easyllm_id": embedding_config["easyllm_id"],
            "dimensions": 768
        }

        # 加载当天的向量数据库
        from datetime import datetime
        today = datetime.now().strftime("%Y%m%d")
        vector_db_path = f"news_vectors_{today}"

        print(f"📅 使用当天向量数据库: {vector_db_path}")
        self.vector_db = VectorDatabase(vector_db_path, vector_dim=768)

        # 数据库配置 - 从环境文件加载
        database_config = get_database_config()
        self.database_config = database_config

        # 初始化数据库管理器
        try:
            self.db_manager = DatabaseManager()
            print("✅ 数据库连接初始化成功")
        except Exception as e:
            print(f"❌ 数据库连接初始化失败: {e}")
            self.db_manager = None

        # 线程锁，确保API调用的线程安全
        self.api_lock = threading.Lock()
        self.last_api_call = 0

        # 根据API key数量动态设置并发线程数
        self.max_workers = min(len(self.llm_config["api_keys"]), 6)  # 最多6个线程

        print("✅ 数据库话题合并器初始化完成")
        print(f"   向量数据库: {self.vector_db.stats['total_documents']} 条新闻")
        print(f"🔧 支持{self.max_workers}线程并发处理，充分利用多API key")
    
    def extract_all_news_titles(self) -> List[str]:
        """提取数据库中所有新闻标题"""
        print(f"📰 提取向量数据库中所有新闻标题...")
        
        all_titles = []
        
        # 遍历所有文档
        for doc_id in self.vector_db.doc_ids:
            doc_info = self.vector_db.get_document(doc_id)
            if doc_info and 'metadata' in doc_info:
                title = doc_info['metadata'].get('title', '').strip()
                if title and len(title) > 5:  # 过滤太短的标题
                    all_titles.append(title)
        
        # 去重（可能有重复的新闻）
        unique_titles = list(set(all_titles))
        
        print(f"✅ 提取完成:")
        print(f"   总标题数: {len(all_titles)}")
        print(f"   去重后: {len(unique_titles)}")
        
        return unique_titles

    def get_thread_api_key(self, thread_id: str) -> tuple:
        """为线程分配专用的API key，返回(key, key_index)"""
        if thread_id not in self.thread_api_keys:
            # 为新线程分配一个API key
            key_index = len(self.thread_api_keys) % len(self.api_keys)
            assigned_key = self.api_keys[key_index]
            self.thread_api_keys[thread_id] = (assigned_key, key_index)
            print(f"🔑 为线程 [{thread_id}] 分配API Key #{key_index + 1}")

        return self.thread_api_keys[thread_id]

    def call_llm(self, prompt: str, thread_id: str = "", max_retries: int = 3) -> str:
        """线程安全的LLM API调用 - 每个线程使用专用API key"""

        # 获取当前线程专用的API key和索引
        api_key, key_index = self.get_thread_api_key(thread_id)

        for retry in range(max_retries):
            try:
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }

                data = {
                    "model": self.llm_config['model'],
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "max_tokens": 16384,
                    "temperature": 0.3
                }

                if thread_id:
                    print(f"   🔄 [{thread_id}] 调用LLM API (使用Key #{key_index + 1}: {api_key[:20]}...)...")

                # 添加小延迟避免频率限制
                import time
                time.sleep(0.5)  # 500ms延迟

                response = requests.post(
                    self.llm_config['base_url'],
                    headers=headers,
                    json=data,
                    timeout=120
                )
                response.raise_for_status()
                result = response.json()

                if thread_id:
                    print(f"   ✅ [{thread_id}] API调用成功")

                return result['choices'][0]['message']['content'].strip()

            except requests.exceptions.HTTPError as e:
                error_msg = f"❌ [{thread_id}] HTTP错误: {e}"
                is_balance_error = False

                if hasattr(e, 'response') and e.response is not None:
                    error_msg += f" | 状态码: {e.response.status_code}"
                    try:
                        error_detail = e.response.json()
                        error_msg += f" | 详情: {error_detail}"
                        # 检查是否是余额不足或频率限制错误
                        if error_detail.get('status') == 20109:
                            is_balance_error = True
                            print(f"   ⚠️ [{thread_id}] API Key #{key_index + 1} ({api_key[:20]}...) 余额不足 (重试 {retry+1}/{max_retries})")
                        elif error_detail.get('status') == 1:
                            print(f"   ⚠️ [{thread_id}] API Key #{key_index + 1} ({api_key[:20]}...) 频率限制，等待重试 (重试 {retry+1}/{max_retries})")
                            import time
                            time.sleep(2)  # 等待2秒后重试
                    except:
                        error_msg += f" | 响应: {e.response.text[:200]}"

                # 如果是余额不足或频率限制且还有重试次数，继续重试
                if (is_balance_error or 'rate limit' in str(e).lower()) and retry < max_retries - 1:
                    continue
                else:
                    print(f"❌ [{thread_id}] API Key #{key_index + 1} ({api_key[:20]}...) 最终失败: {error_msg}")
                    return ""

            except Exception as e:
                error_msg = f"❌ [{thread_id}] API Key #{key_index + 1} ({api_key[:20]}...) 调用异常: {e}"
                print(error_msg)
                return ""

        # 如果所有重试都失败了
        print(f"❌ [{thread_id}] API Key #{key_index + 1} ({api_key[:20]}...) 所有重试失败")
        return ""
    
    def merge_database_topics(self, all_titles: List[str], batch_size: int = 50) -> List[str]:
        """将数据库中的所有标题发给LLM合并话题 - 并发版本"""
        print(f"🔄 开始并发合并数据库中的 {len(all_titles)} 个新闻标题...")

        # 分批准备任务
        batches = []
        for i in range(0, len(all_titles), batch_size):
            batch_titles = all_titles[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(all_titles) + batch_size - 1) // batch_size
            batches.append((batch_titles, batch_num, total_batches))

        print(f"📦 准备并发处理 {len(batches)} 个批次，使用 {self.max_workers} 个线程...")

        merged_topics = []

        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有批次任务
            future_to_batch = {
                executor.submit(self._process_topic_batch, batch_titles, batch_num, total_batches): (batch_num, batch_titles)
                for batch_titles, batch_num, total_batches in batches
            }

            # 收集结果
            batch_results = {}
            for future in as_completed(future_to_batch):
                batch_num, batch_titles = future_to_batch[future]
                try:
                    result = future.result()
                    if result:
                        batch_results[batch_num] = result
                        print(f"   ✅ [并发] 批次 {batch_num} 完成，得到 {len(result)} 个话题")
                    else:
                        print(f"   ❌ [并发] 批次 {batch_num} 失败")
                except Exception as e:
                    print(f"   ❌ [并发] 批次 {batch_num} 异常: {e}")

        # 按批次顺序合并结果
        for batch_num in sorted(batch_results.keys()):
            merged_topics.extend(batch_results[batch_num])

        print(f"\n🎯 并发处理完成")
        print(f"   原始标题: {len(all_titles)} 个")
        print(f"   合并话题: {len(merged_topics)} 个")
        print(f"   处理速度: {self.max_workers}x 并发加速")

        return merged_topics

    def _process_topic_batch(self, batch_titles: List[str], batch_num: int, total_batches: int) -> List[str]:
        """处理单个话题批次"""
        print(f"📦 [T{batch_num}] 开始处理第 {batch_num}/{total_batches} 批次 ({len(batch_titles)} 个标题)...")

        # 构建这批标题的文本
        titles_text = "\n".join([f"{j+1}. {title}" for j, title in enumerate(batch_titles)])

        prompt = f"""你是一名资深的新闻编辑，请对以下新闻标题进行大力度合并分类，目标是将50个标题合并成15-20个话题。

新闻标题列表：
{titles_text}

## 🚨 核心要求：大力度合并，话题名称6-10字！

### 1. 强力合并策略

**✅ 必须大力合并**：
- **同行业必合并**：特斯拉 + 小米汽车 + 比亚迪 + 理想 + 蔚来 → "新能源汽车"
- **同领域必合并**：苹果 + 华为 + 小米手机 + OPPO + vivo → "智能手机"
- **同类型必合并**：抖音 + 快手 + 小红书 → "短视频平台"
- **同公司必合并**：小米手机 + 小米汽车 + 雷军 → "小米动态"
- **同人物必合并**：张学友演唱会 + 张学友新歌 → "张学友动态"
- **同事件必合并**：NBA选秀 + NBA转会 + NBA赛事 → "NBA动态"

### 2. 行业合并标准

**科技类**：
- 新能源汽车：特斯拉、小米汽车、比亚迪、理想、蔚来
- 智能手机：苹果、华为、小米、OPPO、vivo
- AI科技：OpenAI、百度AI、阿里AI、腾讯AI
- 电商平台：淘宝、京东、拼多多

**娱乐类**：
- 短视频：抖音、快手、小红书
- 影视剧：各种电影、电视剧、综艺
- 音乐：各种歌手、演唱会、新歌

**体育类**：
- 篮球：NBA、CBA、各种篮球新闻
- 足球：中超、英超、世界杯、各种足球新闻

**政治类**：
- 中美关系：所有中美相关新闻
- 中东局势：伊朗、以色列、巴勒斯坦相关
- 俄乌冲突：所有俄乌战争相关

### 3. 话题命名要求
- **长度**：严格6-10字
- **格式**：[行业/领域] 或 [公司名] 或 [人物名]
- **示例**：
  - "新能源汽车" (5字) ✅
  - "智能手机" (4字) ✅
  - "小米动态" (4字) ✅
  - "NBA动态" (5字) ✅
  - "中美关系" (4字) ✅

### 4. 合并目标
- 输入50个标题，输出15-20个话题
- 压缩率要达到60-70%
- 每个话题要包含多条相关新闻

**🎯 重要提醒**：
1. 必须大力度合并，不要保守
2. 同行业、同领域的必须合并
3. 话题数量控制在15-20个
4. 每个话题名称6-10字

请严格按照以上要求输出话题列表，每行一个话题，不要编号："""

        # 调用LLM
        response = self.call_llm(prompt, f"BATCH-{batch_num}")

        if response:
            # 解析这批的合并结果
            batch_topics = []
            for line in response.split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and len(line) > 3:
                    # 移除可能的编号
                    if line[0].isdigit() and '.' in line[:5]:
                        line = line.split('.', 1)[1].strip()
                    batch_topics.append(line)

            return batch_topics
        else:
            return []
    
    def calculate_topic_similarity(self, topic1: str, topic2: str) -> float:
        """计算两个话题的相似度"""
        try:
            # 使用简单的关键词重叠度计算相似性
            words1 = set(topic1.lower().split())
            words2 = set(topic2.lower().split())

            if not words1 or not words2:
                return 0.0

            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))

            # Jaccard相似度
            jaccard_sim = intersection / union if union > 0 else 0.0

            # 长度相似度（避免长短差异过大的话题合并）
            len_sim = min(len(topic1), len(topic2)) / max(len(topic1), len(topic2))

            # 综合相似度
            return (jaccard_sim * 0.7 + len_sim * 0.3)

        except Exception as e:
            print(f"   ⚠️ 计算相似度时出错: {e}")
            return 0.0

    def smart_topic_grouping(self, topics: List[str]) -> List[List[str]]:
        """智能话题分组"""
        print(f"\n🧠 开始智能话题分组...")

        if not topics:
            return []

        groups = []
        processed = set()

        for i, topic in enumerate(topics):
            if topic in processed:
                continue

            # 为当前话题寻找相似话题
            current_group = [topic]
            processed.add(topic)

            for j, other_topic in enumerate(topics[i+1:], i+1):
                if other_topic in processed:
                    continue

                similarity = self.calculate_topic_similarity(topic, other_topic)

                # 相似度阈值：0.65以上才合并
                if similarity >= 0.65:
                    current_group.append(other_topic)
                    processed.add(other_topic)

            groups.append(current_group)

        print(f"   📊 分组结果: {len(topics)} 个话题 → {len(groups)} 个组")
        return groups

    def final_merge_topics(self, batch_topics: List[str]) -> List[str]:
        """对分批处理的结果进行智能合并 - 平衡数量与质量"""
        print(f"\n🔄 对 {len(batch_topics)} 个话题进行智能合并...")

        # 先进行智能分组
        topic_groups = self.smart_topic_grouping(batch_topics)

        # 计算目标话题数量（保持合理数量）
        target_min = max(25, len(batch_topics) // 4)  # 至少25个，或原数量的1/4
        target_max = max(40, len(batch_topics) // 2)  # 至少40个，或原数量的1/2

        print(f"   🎯 目标话题数量: {target_min}-{target_max} 个")

        # 使用智能分组进行合并
        final_topics = []

        for i, group in enumerate(topic_groups):
            group_size = len(group)

            if group_size >= 4:
                # 大组：进一步细分或深度合并
                print(f"   � 处理大组 {i+1}: {group_size} 个话题")
                merged_topic = self.merge_large_group(group)
                if merged_topic:
                    final_topics.append(merged_topic)

            elif group_size >= 2:
                # 中等组：直接合并
                print(f"   🔗 处理中等组 {i+1}: {group_size} 个话题")
                merged_topic = self.merge_medium_group(group)
                if merged_topic:
                    final_topics.append(merged_topic)

            else:
                # 单个话题：保持独立
                print(f"   📄 保持独立话题 {i+1}: {group[0]}")
                final_topics.append(group[0])

        # 如果话题数量不够，进行二次合并
        if len(final_topics) < target_min:
            print(f"   🔄 话题数量不足，进行二次合并...")
            final_topics = self.secondary_merge(final_topics, target_min)

        # 如果话题数量过多，进行优化筛选
        elif len(final_topics) > target_max:
            print(f"   ✂️ 话题数量过多，进行优化筛选...")
            final_topics = self.optimize_topics(final_topics, target_max)

        compression_ratio = len(final_topics) / len(batch_topics) * 100
        print(f"\n🎯 智能合并完成")
        print(f"   原始话题: {len(batch_topics)} 个")
        print(f"   最终话题: {len(final_topics)} 个")
        print(f"   保留率: {compression_ratio:.1f}%")
        print(f"   分组数量: {len(topic_groups)} 个")

        if len(final_topics) < target_min:
            print(f"⚠️ 话题数量偏少，建议检查合并策略")

        merged_topics = final_topics

        return merged_topics

    def merge_large_group(self, group: List[str]) -> str:
        """处理大组话题（4个以上）"""
        try:
            # 对大组进行深度合并，提取共同主题
            topics_text = "\n".join([f"{i+1}. {topic}" for i, topic in enumerate(group)])

            prompt = f"""请将以下相关话题合并成一个核心主题，要求简洁明确（6-8字）：

{topics_text}

要求：
1. 提取共同的核心主题
2. 话题名称6-8字以内
3. 能涵盖所有子话题的核心内容

只返回合并后的话题名称："""

            response = self.call_llm(prompt, f"大组合并")
            if response:
                merged_topic = response.strip().split('\n')[0]
                return merged_topic[:20]  # 限制长度
            return group[0]  # 失败时返回第一个

        except Exception as e:
            print(f"   ⚠️ 大组合并失败: {e}")
            return group[0]

    def merge_medium_group(self, group: List[str]) -> str:
        """处理中等组话题（2-3个）"""
        try:
            if len(group) == 2:
                # 两个话题，寻找共同点
                prompt = f"""请将以下两个相关话题合并成一个主题：

1. {group[0]}
2. {group[1]}

要求：话题名称6-8字以内，体现两者共同点。
只返回合并后的话题名称："""
            else:
                # 三个话题
                topics_text = "\n".join([f"{i+1}. {topic}" for i, topic in enumerate(group)])
                prompt = f"""请将以下相关话题合并成一个主题：

{topics_text}

要求：话题名称6-8字以内，体现共同核心。
只返回合并后的话题名称："""

            response = self.call_llm(prompt, f"中组合并")
            if response:
                merged_topic = response.strip().split('\n')[0]
                return merged_topic[:20]
            return group[0]

        except Exception as e:
            print(f"   ⚠️ 中组合并失败: {e}")
            return group[0]

    def secondary_merge(self, topics: List[str], target_min: int) -> List[str]:
        """二次合并：当话题数量不足时"""
        if len(topics) >= target_min:
            return topics

        print(f"   🔄 进行二次合并，目标增加到 {target_min} 个话题")

        # 重新分组，使用更低的阈值
        groups = self.smart_topic_grouping_with_threshold(topics, 0.45)

        final_topics = []
        for group in groups:
            if len(group) > 1:
                merged = self.merge_medium_group(group)
                final_topics.append(merged)
            else:
                final_topics.append(group[0])

        return final_topics

    def optimize_topics(self, topics: List[str], target_max: int) -> List[str]:
        """优化筛选：当话题数量过多时"""
        if len(topics) <= target_max:
            return topics

        print(f"   ✂️ 优化筛选，从 {len(topics)} 个减少到 {target_max} 个")

        # 简单策略：保留前target_max个（可以后续优化为质量排序）
        return topics[:target_max]

    def smart_topic_grouping_with_threshold(self, topics: List[str], threshold: float) -> List[List[str]]:
        """使用指定阈值进行话题分组"""
        groups = []
        processed = set()

        for i, topic in enumerate(topics):
            if topic in processed:
                continue

            current_group = [topic]
            processed.add(topic)

            for j, other_topic in enumerate(topics[i+1:], i+1):
                if other_topic in processed:
                    continue

                similarity = self.calculate_topic_similarity(topic, other_topic)

                if similarity >= threshold:
                    current_group.append(other_topic)
                    processed.add(other_topic)

            groups.append(current_group)

        return groups

    def _process_final_merge_batch(self, batch_chunk: List[str], batch_num: int, total_batches: int) -> List[str]:
        """处理单个最终合并批次"""
        print(f"📦 [F{batch_num}] 开始最终合并第 {batch_num}/{total_batches} 批次 ({len(batch_chunk)} 个话题)...")

        # 构建这批话题的文本
        topics_text = "\n".join([f"{j+1}. {topic}" for j, topic in enumerate(batch_chunk)])

        prompt = f"""你是一名顶级新闻主编，你的任务是从以下杂乱的新闻标题列表中，识别并提炼出具体、独立、有深度报道价值的"新闻事件"。

新闻标题列表：
{topics_text}

## 🚨 核心任务：识别"新闻事件"，而非"合并话题"！

### 1. "新闻事件"的定义：
- 必须是一个具体的、已发生或正在发生的独立事件
- 必须有清晰的主体和动作
- 事件名称应高度概括，本身就是一条微新闻

### 2. 事件提炼原则：

**✅ 精准提炼 (DO):**
- **同一事件，不同侧面 -> 提炼为同一事件**
  - "小米SU7开启交付" + "雷军回应小米汽车定价" + "首批小米SU7车主谈体验"
  - **✅ 提炼为 -> "小米SU7上市交付与市场反响"**
- **同一主体，不同事件 -> 保持为不同事件**
  - "苹果发布iOS 18" + "苹果Vision Pro销量下滑"
  - **✅ 提炼为 -> "苹果发布iOS 18系统" 和 "苹果Vision Pro销量遇冷" (这是两个独立事件!)**

**❌ 严禁过度合并 (DON'T):**
- **不同主体，同一领域 -> 绝对禁止合并！**
  - "小米SU7发布" + "特斯拉Model Y降价"
  - **❌ 错误合并 -> "新能源汽车市场动态" (这是无效话题!)**
- **抽象、宽泛、无具体事件 -> 彻底抛弃！**
  - "科技行业展望"、"手机市场分析" (这些都是没有报道价值的空洞话题)

### 3. 事件命名标准 (6-15字):
- 格式：[主体] + [核心事件/动作] + [结果/影响]
- 示例：
  - ✅ "英伟达发布新一代AI芯片"
  - ✅ "OpenAI CEO奥特曼访问中国"
  - ✅ "NBA独行侠队高薪续约欧文"
  - ✅ "虐猫考生被南京大学拒录"

### 4. 输出要求：
- 从给定的标题中，提炼出大约10-15个最重要、最具体的"新闻事件"
- 如果一个标题无法归入任何具体事件，直接丢弃
- 每行输出一个"新闻事件"，不要编号
- 事件名称6-15字以内

**🎯 你的价值在于发现"金矿"（具体事件），而不是把所有石头（标题）都混在一起。开始工作。**

请严格按照以上标准输出新闻事件列表，每行一个事件，不要编号："""

        # 调用LLM
        response = self.call_llm(prompt, f"FINAL-{batch_num}")

        if response:
            # 解析这批的合并结果
            batch_merged = []
            for line in response.split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and len(line) > 3:
                    # 移除可能的编号
                    if line[0].isdigit() and '.' in line[:5]:
                        line = line.split('.', 1)[1].strip()
                    batch_merged.append(line)

            return batch_merged
        else:
            return []
    
    def process_database_topics(self) -> Dict:
        """完整的数据库话题处理流程"""
        print("🚀 开始处理数据库话题...")
        print("=" * 60)
        
        # 步骤1: 提取所有新闻标题
        all_titles = self.extract_all_news_titles()
        
        if not all_titles:
            print("❌ 未找到新闻标题")
            return {}
        
        # 步骤2: 分批合并话题
        batch_topics = self.merge_database_topics(all_titles, batch_size=50)
        
        # 步骤3: 最终合并
        final_topics = self.final_merge_topics(batch_topics)
        
        # 保存结果
        results = {
            'total_news': len(all_titles),
            'original_titles': all_titles,
            'batch_topics': batch_topics,
            'final_topics': final_topics,
            'compression_ratio': f"{len(all_titles)} → {len(final_topics)} ({len(final_topics)/len(all_titles)*100:.1f}%)"
        }

        return results

    def process_single_topic(self, topic: str, topic_index: int, total_topics: int) -> Dict:
        """处理单个话题的完整流程（用于并发）"""
        thread_id = f"T{topic_index:02d}"

        print(f"\n📝 [{thread_id}] 开始处理话题 {topic_index}/{total_topics}")
        print(f"   话题: {topic}")
        print("-" * 50)

        try:
            # 步骤1: 搜索相关新闻
            news_results = self.find_related_news_for_topic(topic, thread_id=thread_id)

            if not news_results:
                print(f"   ⚠️ [{thread_id}] 未找到相关新闻，跳过该话题")
                return None

            # 根据找到的新闻数量调整处理策略
            news_count = len(news_results)
            if news_count <= 2:
                print(f"   📝 [{thread_id}] 新闻较少({news_count}条)，将简化处理")
            elif news_count >= 15:
                print(f"   📰 [{thread_id}] 新闻丰富({news_count}条)，将深度分析")

            # 步骤2: 总结新闻要点
            print(f"   📋 [{thread_id}] 总结新闻要点...")
            news_points = self.summarize_news_points(topic, news_results, thread_id)

            # 步骤3: 生成爆款文章
            print(f"   ✍️ [{thread_id}] 生成爆款文章...")
            article = self.generate_engaging_article(topic, news_points, thread_id)

            # 步骤4: 准备数据库存储数据
            print(f"   💾 [{thread_id}] 准备数据库存储...")

            # 返回结果
            result = {
                'topic': topic,
                'news_points': news_points,
                'article': article,
                'word_count': len(article),
                'news_count': len(news_results),
                'news_sources': [r['metadata'].get('source', '未知') for r in news_results[:3]],
                'top_news_titles': [r['metadata'].get('title', '无标题') for r in news_results[:3]]
            }

            print(f"   ✅ [{thread_id}] 话题处理完成")
            return result

        except Exception as e:
            print(f"   ❌ [{thread_id}] 处理失败: {e}")
            return None

    def get_topic_embedding(self, topic: str):
        """获取话题的向量"""
        url = self.embedding_config['base_url'].format(
            project_id=self.embedding_config['project_id']
        )

        headers = {
            "Authorization": f"Bearer {self.embedding_config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "easyllm_id": self.embedding_config['easyllm_id'],
            "input_texts": [topic],
            "dimensions": self.embedding_config['dimensions']
        }

        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()
            return result['data'][0]['embedding']
        except Exception as e:
            print(f"❌ 获取话题向量失败: {e}")
            return None



    def find_related_news_for_topic(self, event: str, max_k: int = 30, thread_id: str = "") -> List[Dict]:
        """为新闻事件精确搜索相关新闻 - 事件驱动版本"""
        print(f"🔍 [{thread_id}] 为事件精确搜索新闻: {event[:50]}...")

        # 1. 生成锚点问题
        anchor_question = self._generate_anchor_question(event, thread_id)
        if not anchor_question:
            print(f"  ❌ [{thread_id}] 无法生成锚点问题")
            return []

        # 2. 用问题向量进行搜索
        print(f"  🎯 [{thread_id}] 使用锚点问题搜索: {anchor_question[:100]}...")
        question_embedding = self.get_topic_embedding(anchor_question)
        if not question_embedding:
            return []

        # 适中的阈值，平衡相关性和数量
        initial_results = self.vector_db.search(question_embedding, top_k=max_k, threshold=0.4)

        # 3. 结果校验过滤
        if not initial_results:
            print(f"  ❌ [{thread_id}] 未找到初步结果")
            return []

        print(f"  🛡️ [{thread_id}] 对 {len(initial_results)} 条初步结果进行最终校验...")
        pure_results = self._validate_and_filter_results(event, initial_results, thread_id)

        print(f"  ✅ [{thread_id}] 找到 {len(pure_results)} 条高度相关的纯净新闻")
        return pure_results

    def _generate_anchor_question(self, event: str, thread_id: str = "") -> str:
        """生成锚点问题用于精确搜索"""
        prompt = f"""你是一个信息检索专家。请将以下新闻事件，转换成一个用于在向量数据库中进行精确搜索的核心问题。

新闻事件：{event}

要求：
- 问题要具体但不过于复杂，便于匹配新闻内容
- 包含事件的核心关键词
- 语言自然，像真实用户的搜索需求

示例：
- 事件: "小米SU7上市交付与市场反响"
- 问题: "小米SU7汽车上市交付情况和用户反馈如何？"
- 事件: "独行侠高薪续约欧文"
- 问题: "独行侠队与欧文续约的具体情况和合同细节是什么？"
- 事件: "习近平总书记重要讲话"
- 问题: "习近平总书记最新重要讲话的主要内容和核心观点是什么？"

现在，请为以下事件生成核心问题：
新闻事件：{event}

只输出生成的问题，不要其他内容。"""

        try:
            response = self.call_llm(prompt, f"锚点问题-{thread_id}")
            if response:
                return response.strip()
            return ""
        except Exception as e:
            print(f"  ⚠️ [{thread_id}] 生成锚点问题失败: {e}")
            return ""

    def _validate_and_filter_results(self, event: str, search_results: List[Dict], thread_id: str = "") -> List[Dict]:
        """验证和过滤搜索结果"""
        if not search_results:
            return []

        # 构建新闻标题列表
        news_titles = [result.get('metadata', {}).get('title', '') for result in search_results if result.get('metadata', {}).get('title')]
        if not news_titles:
            return []

        titles_text = "\n".join([f"{i+1}. {title}" for i, title in enumerate(news_titles)])

        prompt = f"""主编，请检查以下新闻标题列表，是否都严格围绕核心事件"{event}"展开。

核心事件：
{event}

待检查的新闻标题列表：
{titles_text}

要求：
- 逐一判断每个标题
- 如果标题与核心事件高度相关，标记为"保留"
- 如果标题只是沾边，或者讲述的是另一个事件，标记为"剔除"

**重要：请严格按照以下JSON格式返回，不要添加任何其他文字：**
[
{{"title": "标题1", "decision": "保留"}},
{{"title": "标题2", "decision": "剔除"}},
{{"title": "标题3", "decision": "保留"}}
]

你必须非常严格，宁可错杀，不能放过一个无关项。只返回JSON，不要其他解释。"""

        try:
            response = self.call_llm(prompt, f"结果验证-{thread_id}")
            if not response:
                return search_results  # 验证失败时返回原结果

            # 多策略JSON解析
            import json
            import re

            filtered_results = []

            try:
                # 策略1：直接JSON解析
                decisions = json.loads(response.strip())

            except json.JSONDecodeError:
                try:
                    # 策略2：提取JSON部分
                    json_match = re.search(r'\[.*\]', response, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                        decisions = json.loads(json_str)
                    else:
                        raise ValueError("未找到JSON格式")

                except (json.JSONDecodeError, ValueError):
                    try:
                        # 策略3：逐行解析决策
                        print(f"  🔧 [{thread_id}] 使用备用解析策略")
                        decisions = self._parse_decisions_fallback(response, news_titles)

                    except Exception:
                        # 策略4：保守策略 - 保留所有结果
                        print(f"  ⚠️ [{thread_id}] 所有解析策略失败，保留所有结果")
                        return search_results

            # 根据决策过滤结果
            for i, decision in enumerate(decisions):
                if i < len(search_results) and decision.get('decision') == '保留':
                    filtered_results.append(search_results[i])

            print(f"  🛡️ [{thread_id}] 验证过滤: {len(search_results)} → {len(filtered_results)} 条")
            return filtered_results

        except Exception as e:
            print(f"  ⚠️ [{thread_id}] 结果验证失败: {e}")
            return search_results

    def _parse_decisions_fallback(self, response: str, news_titles: List[str]) -> List[Dict]:
        """备用解析策略：从文本中提取决策"""
        decisions = []

        # 将响应按行分割
        lines = response.strip().split('\n')

        for i, title in enumerate(news_titles):
            decision = {"title": title, "decision": "保留"}  # 默认保留

            # 在响应中查找相关决策
            for line in lines:
                if any(keyword in line for keyword in ["剔除", "删除", "移除", "不相关"]):
                    # 检查这行是否提到了当前标题的关键词
                    title_keywords = title.split()[:3]  # 取标题前3个词
                    if any(keyword in line for keyword in title_keywords):
                        decision["decision"] = "剔除"
                        break

            decisions.append(decision)

        return decisions

    def _generate_smart_keywords_with_llm(self, topic: str, thread_id: str = "") -> List[str]:
        """🎯 核心方法：用LLM生成最佳搜索关键词"""
        print(f"   🧠 [{thread_id}] LLM生成搜索关键词...")

        prompt = f"""你是一名专业的信息检索专家，请为以下话题生成最佳的搜索关键词。

话题：{topic}

## 任务要求：
必须生成恰好3个搜索关键词，不能多也不能少！确保能在新闻数据库中找到相关内容。

## 关键词生成策略：

### 1. 核心实体提取
- 提取话题中的核心人物、公司、产品、地点
- 例如："小米汽车动态" → 提取"小米"、"汽车"

### 2. 关键词层次化
- **核心词**：最重要的主体（如：小米、特斯拉、苹果）
- **领域词**：所属行业（如：汽车、手机、科技）
- **事件词**：具体事件（如：发布、降价、财报）

### 3. 搜索优化
- 使用常见的新闻用词
- 避免过于抽象的概念
- 确保关键词在新闻标题中常见

### 4. 示例参考：

**话题**："小米汽车动态"
**关键词**：
- 小米
- 汽车
- 小米汽车
- 雷军
- 新能源

**话题**："特斯拉降价风波"
**关键词**：
- 特斯拉
- 降价
- 马斯克
- 电动车
- 价格

**话题**："NBA选秀大会"
**关键词**：
- NBA
- 选秀
- 篮球
- 球员
- 新秀

## 输出格式：
请直接输出3个关键词，每行一个，不要编号或其他格式："""

        try:
            response = self.call_llm(prompt, f"KEYWORD-GEN-{thread_id}")

            if response:
                # 解析关键词
                keywords = []
                for line in response.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#') and len(line) >= 2:
                        # 移除可能的编号和标点
                        line = line.replace('1.', '').replace('2.', '').replace('3.', '').replace('4.', '').replace('5.', '')
                        line = line.replace('-', '').replace('*', '').replace('•', '').strip()
                        if len(line) >= 2 and len(line) <= 10:  # 关键词长度限制
                            keywords.append(line)

                if keywords:
                    print(f"   ✅ [{thread_id}] LLM生成关键词: {keywords}")
                    return keywords[:3]  # 最多3个关键词
                else:
                    print(f"   ❌ [{thread_id}] LLM未生成有效关键词")
                    return self._extract_simple_keywords(topic)  # 降级到简单提取
            else:
                print(f"   ❌ [{thread_id}] LLM调用失败")
                return self._extract_simple_keywords(topic)  # 降级到简单提取

        except Exception as e:
            print(f"   ❌ [{thread_id}] LLM关键词生成异常: {e}")
            return self._extract_simple_keywords(topic)  # 降级到简单提取

    def _extract_simple_keywords(self, topic: str) -> List[str]:
        """简单粗暴提取关键词"""
        import re

        # 移除标点符号，按常见分隔符拆分
        clean_topic = re.sub(r'[：:（）()【】\[\]《》<>""''、，,。.！!？?]', ' ', topic)
        words = clean_topic.split()

        keywords = []

        # 提取2字以上的词
        for word in words:
            word = word.strip()
            if len(word) >= 2:
                keywords.append(word)

        # 如果关键词太少，尝试拆分长词
        if len(keywords) < 3:
            for word in words:
                if len(word) >= 4:
                    # 尝试拆分成2字词
                    for i in range(len(word) - 1):
                        sub_word = word[i:i+2]
                        if len(sub_word) == 2:
                            keywords.append(sub_word)

        # 去重并限制数量
        unique_keywords = []
        seen = set()
        for kw in keywords:
            if kw not in seen:
                seen.add(kw)
                unique_keywords.append(kw)

        return unique_keywords[:5]  # 最多5个关键词



    def _deduplicate_and_rank(self, results: List[Dict]) -> List[Dict]:
        """去重和排序"""
        seen_ids = set()
        unique_results = []

        for result in results:
            doc_id = result.get('doc_id', '')
            if doc_id and doc_id not in seen_ids:
                seen_ids.add(doc_id)
                unique_results.append(result)

        # 按相关度排序
        unique_results.sort(key=lambda x: x.get('relevance_score', x.get('similarity', 0)), reverse=True)

        # 限制最终数量
        return unique_results[:15]

    def _intelligent_filter_news(self, topic: str, news_results: List[Dict]) -> List[Dict]:
        """智能过滤新闻：基于关键词匹配和语义相关性"""
        if not news_results:
            return []

        # 提取话题关键词
        topic_keywords = self._extract_topic_keywords(topic)

        filtered_news = []
        for news in news_results:
            title = news['metadata'].get('title', '')
            content = news['metadata'].get('content', '')

            # 计算关键词匹配度
            keyword_score = self._calculate_keyword_match(topic_keywords, title, content)

            # 计算语义相关度（基于向量相似度）
            semantic_score = news.get('similarity', 0)

            # 综合评分
            combined_score = keyword_score * 0.6 + semantic_score * 0.4

            # 设置过滤阈值
            if combined_score > 0.3:
                news['relevance_score'] = combined_score
                filtered_news.append(news)

        # 按相关度排序
        filtered_news.sort(key=lambda x: x['relevance_score'], reverse=True)

        return filtered_news

    def _extract_topic_keywords(self, topic: str) -> List[str]:
        """从话题中提取关键词"""
        # 简单的关键词提取（可以后续优化为更复杂的NLP）
        import re

        # 移除常见的停用词
        stop_words = {'的', '和', '与', '及', '或', '等', '相关', '动态', '最新', '事件', '新闻', '报道'}

        # 提取中文词汇和英文单词
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', topic)

        # 过滤停用词和短词
        keywords = [word for word in words if len(word) >= 2 and word not in stop_words]

        return keywords

    def _calculate_keyword_match(self, keywords: List[str], title: str, content: str) -> float:
        """计算关键词匹配度"""
        if not keywords:
            return 0.0

        text = (title + ' ' + content).lower()
        matched_keywords = 0

        for keyword in keywords:
            if keyword.lower() in text:
                matched_keywords += 1

        return matched_keywords / len(keywords)

    def _quality_filter_news(self, topic: str, news_results: List[Dict]) -> List[Dict]:
        """质量过滤：确保新闻质量和相关性"""
        if not news_results:
            return []

        quality_news = []

        for news in news_results:
            title = news['metadata'].get('title', '')
            content = news['metadata'].get('content', '')
            relevance_score = news.get('relevance_score', 0)

            # 质量检查
            quality_checks = [
                len(title) >= 5,  # 标题不能太短
                len(content) >= 20,  # 内容不能太短
                relevance_score >= 0.3,  # 相关度不能太低
                not self._is_spam_content(title, content)  # 不是垃圾内容
            ]

            if all(quality_checks):
                quality_news.append(news)

        # 限制最终数量，避免信息过载
        max_final_count = 15
        return quality_news[:max_final_count]

    def _is_spam_content(self, title: str, content: str) -> bool:
        """检查是否为垃圾内容"""
        spam_indicators = [
            '广告', '推广', '点击', '链接', '下载',
            '免费', '优惠', '折扣', '特价', '限时'
        ]

        text = (title + ' ' + content).lower()

        for indicator in spam_indicators:
            if indicator in text:
                return True

        return False

    def summarize_news_points(self, topic: str, news_results: List[Dict], thread_id: str = "") -> str:
        """步骤3: 总结新闻要点"""
        print(f"📋 [{thread_id}] 为话题总结新闻要点: {topic[:50]}...")

        if not news_results:
            return f"未找到关于 '{topic}' 的相关新闻。"

        # 构建新闻素材
        news_content = ""
        for i, result in enumerate(news_results, 1):
            metadata = result['metadata']
            news_content += f"\n【新闻{i}】\n"
            news_content += f"标题: {metadata.get('title', '无标题')}\n"
            news_content += f"来源: {metadata.get('source', '未知')} | 时间: {metadata.get('publish_time', '未知')}\n"
            news_content += f"内容: {metadata.get('content', '无内容')[:500]}...\n"
            news_content += "-" * 30 + "\n"

        # 构建要点总结提示词
        prompt = f"""请基于以下新闻内容，总结出关于话题 "{topic}" 的核心要点。

话题: {topic}

相关新闻:
{news_content}

要求:
1. 提取与话题最相关的核心信息和关键事实
2. 按重要性排序，列出5-8个要点
3. 每个要点包含：事实描述、背景信息、影响分析
4. 识别争议点、热点、趋势和深层原因
5. 保持客观准确，但要挖掘新闻背后的故事
6. 为后续文章创作提供丰富素材

请输出要点总结："""

        # 调用LLM总结要点
        summary = self.call_llm(prompt, f"要点总结-{thread_id}")

        if summary:
            print(f"   ✅ [{thread_id}] 要点总结完成 ({len(summary)} 字符)")
            return summary
        else:
            print(f"   ❌ [{thread_id}] 要点总结失败")
            return f"要点总结失败：{topic}"

    def generate_engaging_article(self, topic: str, news_points: str, thread_id: str = "") -> str:
        """步骤4: 基于要点生成吸引读者的文章"""
        print(f"✍️ [{thread_id}] 基于要点生成吸引性文章: {topic[:50]}...")

        if not news_points or "失败" in news_points:
            return f"无法为话题 '{topic}' 生成文章，缺少有效要点。"

        # 构建虎扑风格的文章生成提示词
        prompt = f"""你是虎扑体育的资深编辑，擅长写出客观、流畅、有深度的体育新闻文章。

请基于以下新闻要点，围绕事件"{topic}"写一篇虎扑风格的深度文章。

**核心事件**: {topic}

**新闻要点 (已确认全部与核心事件相关)**:
{news_points}

## 🚨 核心写作纪律：
**本文必须且只能围绕"{topic}"这一个核心事件展开。** 严禁发散到其他不相关的人物或事件上。你的任务是把这一个事件讲深、讲透，而不是做一个新闻盘点。从第一个字到最后一个字，都必须服务于这个核心事件。

## 虎扑风格写作要求：

### 1. 文章定位
- **客观叙述**：以第三人称视角，客观描述事件和人物
- **深度挖掘**：不只是表面新闻，要挖掘背后的故事和细节
- **专业分析**：基于事实进行合理分析，有理有据

### 2. 标题策略（15-25字）
- **强钩子优先**：标题必须有吸引力，让人忍不住点开
- **钩子技巧**：
  * 引用式："XXX，下赛季我罩着你"
  * 反转式："手术成功！哈利伯顿：即使跟腱撕裂也不后悔"
  * 悬念式："这个决定，改变了整个联盟格局"
  * 对比式："从替补到核心，他只用了一个赛季"
  * 冲突式："球迷怒了：这笔交易简直是抢劫"
- **情感词汇**：适当使用"震撼"、"意外"、"逆转"、"爆发"等词
- **数字冲击**：用具体数字增强冲击力

### 3. 文章结构（总计1000-1500字）

**开头段（150-200字）- 场景描述**
- 用具体的时间、地点、人物开场
- 描述一个具体的场景或事件
- 如果内容适合，可以用有趣的细节或对比作为钩子
- 语言平实，但可以适当增加吸引力
- 例如："2025年6月29日，这是被开拓者选中的杨瀚森第一天到球队报道的日子。"

**主体部分（700-1100字）- 层层展开**
- **🚨 绝对不要用（1）（2）（3）分点！**
- 按时间线或逻辑线自然展开
- 多用具体的细节和数据
- 引用当事人的话语和行为
- 挖掘事件背后的深层原因
- 适当使用对比、反转等手法增加可读性
- 语言客观，但可以有适度的叙述技巧

**结尾段（150-200字）- 升华总结**
- 总结事件的意义或影响
- 可以适当展望未来
- 语调平和，不过分煽情
- 让读者对事件有更深理解

### 4. 写作技巧

**客观叙述**：
- 以第三人称视角描述，避免"我觉得"、"我认为"
- 用事实说话，让读者自己得出结论
- 保持中性立场，不带个人情感色彩

**细节刻画**：
- 用具体的时间、地点、数据增强真实感
- 描述人物的具体行为和表情
- 引用原话时要准确，增加可信度

**逻辑连贯**：
- 段落之间要有自然的过渡
- 按照时间线或因果关系组织内容
- 每个段落都要为主题服务

### 5. 语言风格
- **平实客观**：语言朴实，不华丽不煽情
- **专业准确**：体育术语使用准确，数据真实
- **流畅易读**：句子长短适中，逻辑清晰
- **有深度**：不只是表面信息，要有分析和思考

### 6. 🚨 绝对禁止（重要！）
- ❌ **使用（1）（2）（3）或第一、第二、第三等分点**
- ❌ **使用第一人称"我"、"我们"等**
- ❌ **过度煽情或主观评判**
- ❌ **使用网络流行语或过于口语化表达**
- ❌ **夸大事实或添加不实信息**

### 7. 虎扑风格检查清单
✅ **标题必须有强钩子**，让人忍不住想点开
✅ 开头用具体场景描述，有画面感
✅ 全文第三人称客观叙述
✅ 多用具体细节和准确数据
✅ 逻辑清晰，段落间自然过渡
✅ 语言平实专业，适当使用叙述技巧增加可读性
✅ 在保持客观的前提下，让文章有吸引力

**🎯 最终要求**：写出一篇像虎扑编辑写的专业体育文章，客观、深度、有料，标题要有强烈的点击欲望！

**🔥 特别强调：标题是文章成功的关键！**
- 标题必须有强烈的钩子，让读者产生"这是怎么回事？"的好奇心
- 可以用引用、反转、悬念、对比、冲突等手法
- 要让人看到标题就忍不住想点开

请创作文章："""

        # 调用LLM生成文章
        article = self.call_llm(prompt, f"文章生成-{thread_id}")

        if article:
            print(f"   ✅ [{thread_id}] 爆款文章生成完成 ({len(article)} 字符)")
            return article
        else:
            print(f"   ❌ [{thread_id}] 文章生成失败")
            return f"文章生成失败：{topic}"

    def generate_all_articles(self, topics: List[str]) -> Dict:
        """为所有话题生成爆款文章 - 3线程并发处理"""
        print(f"\n🚀 开始为 {len(topics)} 个话题生成爆款文章...")
        print("🔧 使用3线程并发处理，大幅提升效率")
        print("=" * 60)

        articles = {}
        max_workers = self.max_workers  # 根据API key数量动态设置并发数

        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_topic = {
                executor.submit(self.process_single_topic, topic, i+1, len(topics)): topic
                for i, topic in enumerate(topics)
            }

            # 收集结果
            completed_count = 0
            for future in as_completed(future_to_topic):
                topic = future_to_topic[future]
                completed_count += 1

                try:
                    result = future.result()
                    if result:
                        articles[result['topic']] = {
                            'news_points': result['news_points'],
                            'article': result['article'],
                            'news_count': result['news_count'],
                            'news_sources': result['news_sources'],
                            'top_news_titles': result['top_news_titles']
                        }
                        print(f"\n🎉 [{completed_count}/{len(topics)}] 话题完成: {topic[:50]}...")
                    else:
                        print(f"\n⚠️ [{completed_count}/{len(topics)}] 话题跳过: {topic[:50]}...")

                except Exception as e:
                    print(f"\n❌ [{completed_count}/{len(topics)}] 话题失败: {topic[:50]}... - {e}")

        print(f"\n✅ 并发处理完成！成功生成 {len(articles)} 篇爆款文章")

        # 保存文章到数据库
        if articles and self.db_manager:
            print(f"\n💾 开始保存 {len(articles)} 篇文章到数据库...")
            batch_info = {
                'total_articles': len(articles),
                'total_topics': len(topics)
            }

            save_result = self.db_manager.save_articles_batch(articles, batch_info)

            if save_result.get('saved_count', 0) > 0:
                print(f"✅ 数据库保存完成: {save_result['saved_count']}/{save_result['total_articles']} 篇文章")
                print(f"📊 批次ID: {save_result['batch_id']}")
                print(f"⏱️ 处理耗时: {save_result['processing_time_seconds']} 秒")
            else:
                print(f"❌ 数据库保存失败: {save_result.get('error', '未知错误')}")

        return articles


def main():
    """主函数 - 完整的自动化内容生成流程"""
    print("🎯 自动化内容生成系统")
    print("=" * 60)

    # 创建合并器
    merger = DatabaseTopicMerger()

    # 步骤1: 处理数据库话题
    print("📋 步骤1: 从数据库提取并合并话题")
    results = merger.process_database_topics()

    if not results:
        print("❌ 话题处理失败")
        return

    # 显示话题合并结果
    print("\n" + "=" * 60)
    print("📊 话题合并结果")
    print("=" * 60)

    print(f"数据库新闻总数: {results['total_news']}")
    print(f"最终话题数: {len(results['final_topics'])}")
    print(f"压缩比例: {results['compression_ratio']}")

    print(f"\n📋 合并后的话题列表:")
    for i, topic in enumerate(results['final_topics'], 1):
        print(f"{i:2d}. {topic}")

    # 步骤2: 为每个话题生成爆款文章（包含步骤3+4）
    print("\n📝 步骤2: 为每个话题生成爆款文章")
    print("   - 步骤3: 总结新闻要点")
    print("   - 步骤4: 基于要点创作吸引读者的文章")
    articles = merger.generate_all_articles(results['final_topics'])

    # 显示文章生成结果
    print("\n" + "=" * 60)
    print("📰 爆款文章生成结果")
    print("=" * 60)

    print(f"成功生成爆款文章: {len(articles)} 篇")

    # 显示文章摘要
    for i, (topic, article_info) in enumerate(articles.items(), 1):
        article_text = article_info['article']

        # 显示文章摘要
        print(f"\n📄 爆款文章 {i}: {topic[:60]}...")
        print(f"   字数: {len(article_text)} 字符")
        print(f"   素材来源: {', '.join(article_info['news_sources'])}")
        print(f"   相关新闻: {article_info['news_count']} 条")
        print(f"   核心新闻: {', '.join(article_info['top_news_titles'])}")

        # 显示文章开头（钩子效果）
        article_lines = article_text.split('\n')
        title_line = article_lines[0] if article_lines else ""
        content_start = '\n'.join(article_lines[1:3]) if len(article_lines) > 1 else ""
        print(f"   标题: {title_line}")
        print(f"   开头钩子: {content_start[:150]}...")

    print("\n🎉 爆款文章自动化生成完成！")
    print(f"📊 完整流程: {results['total_news']} 条新闻 → {len(results['final_topics'])} 个话题 → {len(articles)} 篇爆款文章")
    print("🎯 每篇文章都经过：话题提取 → 新闻匹配 → 要点总结 → 爆款创作 → 数据库存储")
    print("🔥 文章特色：开头钩子 + 心理学技巧 + 读者导向")
    print("💾 所有文章已保存到数据库，包含完整的时间戳信息")


if __name__ == "__main__":
    main()
