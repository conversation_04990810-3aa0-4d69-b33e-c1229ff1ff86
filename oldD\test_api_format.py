#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API请求格式
"""

import requests
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from config import get_embedding_config, get_llm_config
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_embedding_api():
    """测试embedding API请求格式"""
    print("🧪 测试Embedding API请求格式")
    print("-" * 50)
    
    try:
        embedding_config = get_embedding_config()
        
        url = embedding_config['base_url'].format(
            project_id=embedding_config['project_id']
        )
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {embedding_config['api_key']}"
        }
        
        # 测试数据
        test_texts = ["这是一条测试文本"]
        
        data = {
            "easyllm_id": embedding_config['easyllm_id'],
            "input_texts": test_texts,
            "dimensions": embedding_config['dimensions']
        }
        
        print(f"📡 请求URL: {url}")
        print(f"📋 请求头: {headers}")
        print(f"📄 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Embedding API请求成功")
            print(f"📊 返回数据结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")
            return True
        else:
            print(f"❌ Embedding API请求失败")
            print(f"📄 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Embedding API测试异常: {e}")
        return False

def test_llm_api():
    """测试LLM API请求格式"""
    print("\n🧪 测试LLM API请求格式")
    print("-" * 50)
    
    try:
        llm_config = get_llm_config()
        
        if not llm_config.get("api_keys"):
            print("❌ 没有可用的LLM API Key")
            return False
        
        api_key = llm_config["api_keys"][0]
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": llm_config.get("model", "DeepSeek-V3-Fast"),
            "messages": [
                {"role": "user", "content": "你好，这是一个测试请求。"}
            ],
            "max_tokens": 100,
            "temperature": 0.3
        }
        
        print(f"📡 请求URL: {llm_config.get('base_url', 'https://www.sophnet.com/api/open-apis/chat/completions')}")
        print(f"📋 请求头: {headers}")
        print(f"📄 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            llm_config.get('base_url', 'https://www.sophnet.com/api/open-apis/chat/completions'),
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ LLM API请求成功")
            print(f"📊 返回数据结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")
            return True
        else:
            print(f"❌ LLM API请求失败")
            print(f"📄 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ LLM API测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始API请求格式测试")
    print("=" * 60)
    
    # 测试Embedding API
    embedding_ok = test_embedding_api()
    
    # 测试LLM API
    llm_ok = test_llm_api()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   Embedding API: {'✅ 正常' if embedding_ok else '❌ 异常'}")
    print(f"   LLM API: {'✅ 正常' if llm_ok else '❌ 异常'}")
    
    if embedding_ok and llm_ok:
        print("🎉 所有API请求格式测试通过！")
    else:
        print("⚠️ 存在API请求格式问题，需要修复")

if __name__ == "__main__":
    main()
