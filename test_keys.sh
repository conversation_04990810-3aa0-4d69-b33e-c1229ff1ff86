#!/bin/bash

# 根据主脚本配置测试API Keys
API_KEYS=(
    "JHEsJv1aSv06NTy06K3VsBshU83C6E0XDtA1Yx9rVgKUYKqPgzfo2n8cfcZ_A7vI2BwsYc0Ai0dAI-woPJwtZg"
    "r0283fxCH9SB7UUKqgZq2wybCdND3MEQ8m4ZX9vdoxeYexDL24KICZxtN7gcSRxT24GtXdfF13WHJI2CqbWqLQ"
    "-qRxLu1kcIbuOHMEprPRCvoOmwUCMB2SVosUnchntfHffHHIDa_zBtLOR6tc8pAfdw_n6thuJO1jG21bkDjIAA"
    "aSHiOk6FguzVA4th6-GFgZ59P_SNE5kl24b7m9L861MOdw4BYeFnVxiqrEiUnJ4o46AYrg2OUIdlUn05cPtFqg"
    "Ow_rBGPwNCSkBXT4e0geAmaXRLMhV09vD2P2DIeFT3behy4bD4PPXuei-YyOFJjzgGPod_OIIjlfaPCg0wji7w"
    "fD8zQMDjF7bJy2cTe257T-vJlpfjFO0E_QcO74lIvrdTWSNzcy2Wjc2u2lSCasOEYj1c7uRjRZJn4cWE1wTRgg"
)

echo "🔑 测试6个API Key..."

for i in "${!API_KEYS[@]}"; do
    key_num=$((i + 1))
    api_key="${API_KEYS[$i]}"
    
    echo "测试Key #${key_num}: ${api_key:0:20}..."
    
    # 使用和主脚本完全相同的格式，注意URL路径
    response=$(curl -s -w "\n%{http_code}" \
        -X POST "https://www.sophnet.com/api/open-apis/v1/chat/completions" \
        -H "Authorization: Bearer $api_key" \
        -H "Content-Type: application/json" \
        -d '{
            "model": "DeepSeek-V3-Fast",
            "messages": [
                {"role": "user", "content": "测试"}
            ],
            "max_tokens": 50,
            "temperature": 0.3
        }' \
        --connect-timeout 10 \
        --max-time 30)

    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)

    echo "状态码: $http_code"
    if [ "$http_code" = "200" ]; then
        echo "✅ Key #${key_num} 成功!"
        echo "响应: $response_body"
    else
        echo "❌ Key #${key_num} 失败!"
        echo "错误: $response_body"
    fi
    
    echo -e "\n---\n"
    sleep 1
done
