# 🚀 自动化新闻处理流水线

一个基于多线程和LLM的智能新闻处理系统，实现从爬取到文章生成的全自动化流水线。

## ✨ 功能特性

### 🔄 **三步式并行处理流水线**
1. **📰 步骤1**: 双线程并行爬取（网易+新浪同时进行）
2. **🔄 步骤2**: 多线程向量化处理（3线程并发+分批优化API调用）
3. **✍️ 步骤3**: 多线程并发生成（话题合并+文章创作）

### 🎯 **核心优势**
- **🚀 高性能并发**：多线程处理，大幅提升速度
- **🧠 智能话题合并**：LLM自动识别和合并相似新闻
- **📊 向量化存储**：支持语义搜索和相似度分析
- **💾 数据库集成**：MySQL存储，支持后端系统集成
- **🔑 多API Key轮换**：避免频率限制，提高稳定性

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <your-repo-url>
cd oldD

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件，填入你的API密钥和数据库信息
# 需要配置：LLM API Keys, Embedding API, MySQL数据库
```

### 3. 运行流水线
```bash
# 默认模式：30条新闻，包含详细内容
python automated_news_pipeline.py

# 获取更多新闻
python automated_news_pipeline.py 100

# 获取全部新闻
python automated_news_pipeline.py all

# 测试模式（少量数据）
python automated_news_pipeline.py test
```

## 📁 项目结构

```
oldD/
├── automated_news_pipeline.py    # 主流水线入口
├── config.py                     # 配置管理
├── database_manager.py           # 数据库管理
├── database_topic_merger.py      # 话题合并器（多线程LLM）
├── csv_to_vector_processor.py    # 向量化处理器（多线程）
├── vector_database.py            # 向量数据库
├── database_viewer.py            # 数据库查看器
├── 新闻爬取/                      # 爬虫模块
│   ├── netease_news_crawler.py   # 网易新闻爬虫
│   ├── sina_news_crawler.py      # 新浪新闻爬虫
│   └── unified_news_crawler.py   # 统一爬虫接口
├── qq邮箱发送/                    # 邮件发送（可选）
│   └── qqemail.py
├── setup_cron.sh                 # 定时任务设置脚本
├── scheduled_runner.py           # 定时任务执行器（备用）
├── monitor_status.py             # 定时任务状态监控
├── run_realtime.py               # 实时执行脚本（无缓冲）
├── test_*.py                     # 测试文件
├── logs/                         # 日志目录
│   ├── cron.log                  # 定时任务日志
│   └── scheduled_news_*.log      # 执行详细日志
├── .env.example                  # 环境变量模板
├── requirements.txt              # 依赖包
└── README.md                     # 项目说明
```

## ⚙️ 配置说明

### 必需配置
- **LLM API Keys**: 6个API密钥，支持多线程并发
- **Embedding API**: 用于文本向量化
- **MySQL数据库**: 存储新闻和生成的文章

### 可选配置
- **邮件发送**: QQ邮箱SMTP配置（现已改为数据库存储）

## 🔧 核心模块

### 1. 新闻爬取模块
- **双线程并行爬取**：网易+新浪同时进行
- **智能内容提取**：自动清洗和格式化
- **去重处理**：避免重复新闻

### 2. 向量化处理模块
- **多线程并发**：3个线程同时处理
- **批量优化**：每批10条新闻，减少API调用
- **线程安全**：每个线程使用专用API Key

### 3. 话题合并模块
- **智能分析**：LLM自动识别相似新闻
- **话题生成**：合并相关新闻为热点话题
- **多线程处理**：并发生成多个话题文章

## 📊 性能特性

- **并发处理**：多线程架构，处理速度提升2-3倍
- **API优化**：智能重试机制，自动处理余额不足和频率限制
- **内存优化**：流式处理，支持大量新闻数据
- **错误恢复**：完善的异常处理和重试机制

## ⏰ 定时任务设置

### 🚀 一键设置定时任务
```bash
# 设置每6小时自动执行的定时任务
bash setup_cron.sh
```

### 📋 定时任务管理
```bash
# 查看当前定时任务
crontab -l

# 删除所有定时任务
crontab -r

# 手动编辑定时任务
crontab -e
```

### 🔍 监控定时任务
```bash
# 查看执行状态
python monitor_status.py

# 查看实时日志
tail -f logs/cron.log

# 查看最新执行日志
ls -la logs/scheduled_news_*.log
```

### ⏱️ 执行时间
定时任务会在以下时间自动执行：
- **00:00** (午夜12点)
- **06:00** (早上6点)
- **12:00** (中午12点)
- **18:00** (下午6点)

### 🛠️ 手动执行测试
```bash
# 手动运行完整流水线
python automated_news_pipeline.py all

# 实时监控版本（无缓冲）
python run_realtime.py
```

## 🛠️ 使用示例

### 查看数据库内容
```bash
python database_viewer.py
```

### 测试API连接
```bash
python test_api_keys.py
```

### 测试多线程向量化
```bash
python test_multithread_vectorizer.py
```

## 📝 输出说明

系统会生成以下内容：
1. **新闻数据**：存储在MySQL数据库中
2. **向量数据库**：用于语义搜索
3. **生成文章**：高质量的新闻分析文章
4. **处理日志**：详细的执行过程记录

## 📚 定时任务详细教学

### 🎯 快速部署步骤

1. **环境准备**
```bash
# 确保所有依赖已安装
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件，填入API密钥和数据库配置
```

2. **设置定时任务**
```bash
# 一键设置定时任务
bash setup_cron.sh

# 验证设置成功
crontab -l
```

3. **测试运行**
```bash
# 手动测试一次
python automated_news_pipeline.py all
```

### 🔧 定时任务故障排除

#### 问题1：定时任务不执行
```bash
# 检查cron服务状态
sudo systemctl status cron

# 查看系统日志
sudo tail -f /var/log/cron

# 检查用户权限
ls -la /var/spool/cron/crontabs/
```

#### 问题2：执行失败
```bash
# 查看执行日志
tail -50 logs/cron.log

# 检查Python路径
which python3

# 手动测试命令
cd /path/to/project && python3 automated_news_pipeline.py all
```

#### 问题3：重新设置定时任务
```bash
# 删除旧任务
crontab -r

# 重新设置
bash setup_cron.sh

# 确认设置
crontab -l
```

### 📊 监控和维护

#### 日志管理
```bash
# 查看最新日志
tail -f logs/cron.log

# 查看历史日志
ls -la logs/

# 清理旧日志（保留最近7天）
find logs/ -name "*.log" -mtime +7 -delete
```

#### 性能监控
```bash
# 查看系统资源使用
htop

# 查看磁盘空间
df -h

# 查看数据库大小
python database_viewer.py
```

## ⚠️ 注意事项

1. **API配置**：确保所有API Key都有足够余额
2. **数据库权限**：MySQL用户需要CREATE、INSERT、SELECT权限
3. **网络环境**：需要稳定的网络连接访问新闻网站和API
4. **资源消耗**：多线程处理会消耗较多CPU和内存资源
5. **定时任务**：确保服务器时区设置正确
6. **日志空间**：定期清理日志文件，避免磁盘空间不足

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License