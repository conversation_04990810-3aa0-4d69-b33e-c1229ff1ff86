#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化新闻处理流水线 - 三合一脚本
整合：新闻爬取 → CSV转向量 → 话题合并生成文章

功能流程：
1. 爬取网易+新浪新闻 → 生成CSV文件
2. CSV数据向量化 → 存储到向量数据库  
3. 话题智能合并 → 生成爆款文章 → 发送到映像笔记

使用方法：
python automated_news_pipeline.py [新闻数量] [是否获取详细内容]
例如：
  python automated_news_pipeline.py 50 true    # 每个来源50条，包含详细内容
  python automated_news_pipeline.py all false  # 获取全部新闻，不包含详细内容
"""

import sys
import os
import time
import shutil
import glob
from datetime import datetime
from typing import Optional, Tuple, List, Dict

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入各个模块的核心类
try:
    # 爬虫相关
    sys.path.append(os.path.join(os.path.dirname(__file__), '新闻爬取'))
    from unified_news_crawler import UnifiedNewsCrawler
    
    # 向量化相关
    from csv_to_vector_processor import CSVNewsVectorProcessor
    from config import get_embedding_config
    
    # 话题合并相关
    from database_topic_merger import DatabaseTopicMerger

    # 数据库管理相关
    from database_manager import DatabaseManager
    
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("💡 请确保所有相关文件都在正确位置")
    sys.exit(1)


class AutomatedNewsPipeline:
    """自动化新闻处理流水线"""
    
    def __init__(self, max_news_per_source: int = 50, get_detail: bool = True, get_all: bool = False):
        """
        初始化流水线
        :param max_news_per_source: 每个来源最大新闻数量
        :param get_detail: 是否获取详细内容
        :param get_all: 是否获取所有新闻
        """
        self.max_news_per_source = max_news_per_source
        self.get_detail = get_detail
        self.get_all = get_all
        self.start_time = datetime.now()
        self.today = self.start_time.strftime("%Y%m%d")
        self.timestamp = self.start_time.strftime("%Y%m%d_%H%M%S")
        
        # 数据存储
        self.news_data = None  # 存储新闻数据
        self.vector_db_path = f"news_vectors_{self.today}"
        
        print(f"🚀 自动化新闻处理流水线启动")
        print(f"📅 处理日期: {self.today}")
        print(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⚙️ 配置参数:")
        print(f"   - 每个来源新闻数: {'全部' if self.get_all else self.max_news_per_source}")
        print(f"   - 获取详细内容: {'是' if self.get_detail else '否'}")
        print(f"   - 向量数据库路径: {self.vector_db_path}")
        print("="*80)
    
    def step1_crawl_news(self) -> Tuple[bool, Optional[List], int]:
        """步骤1: 爬取新闻（保持原有的并行特性）"""
        print(f"\n🔥 步骤1: 并行爬取最新新闻")
        print("="*60)

        try:
            # 创建统一爬虫（保持原有的并行爬取特性）
            crawler = UnifiedNewsCrawler(
                max_news_per_source=self.max_news_per_source,
                get_detail=self.get_detail,
                get_all=self.get_all
            )

            # 开始并行爬取（网易和新浪同时进行）
            print(f"🚀 启动双线程并行爬取...")
            if self.get_all:
                print(f"⚙️ 设置: 获取全部新闻, 获取详细内容: {'是' if self.get_detail else '否'}")
            else:
                print(f"⚙️ 设置: 每个来源最多 {self.max_news_per_source} 条, 获取详细内容: {'是' if self.get_detail else '否'}")

            news_data, total_count = crawler.crawl_all()  # 返回数据而不是文件名

            if news_data and total_count > 0:
                self.news_data = news_data  # 存储到内存中

                print(f"✅ 步骤1完成: 并行爬取成功，共 {total_count} 条新闻")
                print(f"💾 数据已加载到内存，无需保存CSV文件")
                print(f"🔥 并行爬取优势: 网易+新浪同时进行，大幅提升效率")
                return True, news_data, total_count
            else:
                print(f"❌ 步骤1失败: 并行爬取失败")
                return False, None, 0

        except Exception as e:
            print(f"❌ 步骤1异常: {e}")
            import traceback
            traceback.print_exc()
            return False, None, 0
    
    def step2_vectorize_news(self, news_data: List[Dict]) -> bool:
        """步骤2: 向量化新闻（批量处理优化）"""
        print(f"\n🔄 步骤2: 新闻数据向量化")
        print("="*60)

        try:
            # 清理旧的向量数据库
            if os.path.exists(self.vector_db_path):
                print(f"🧹 清理旧的向量数据库: {self.vector_db_path}")
                shutil.rmtree(self.vector_db_path)

            # 获取配置
            embedding_config = get_embedding_config()

            # 创建向量化处理器
            processor = CSVNewsVectorProcessor(embedding_config, self.vector_db_path)

            print(f"🚀 启动多线程向量化处理...")
            print(f"⚙️ 批量大小: 10条/批次（优化API调用效率）")
            print(f"⚙️ 线程数: 3个（并发处理，提升速度）")
            print(f"⚙️ 处理策略: 多线程分批处理，避免API限制")

            # 处理新闻数据（多线程处理内存中的数据）
            processor.process_news_data(
                news_data=news_data,
                batch_size=10,    # 批量处理，提高效率
                max_news=None,    # 处理全部新闻
                max_workers=3     # 3个线程并发处理
            )

            # 检查处理结果
            if processor.stats['successful_vectorized'] > 0:
                success_rate = processor.stats['successful_vectorized'] / processor.stats['total_processed'] * 100
                print(f"✅ 步骤2完成: 成功向量化 {processor.stats['successful_vectorized']} 条新闻")
                print(f"📊 成功率: {success_rate:.1f}%")
                print(f"📊 向量数据库: {self.vector_db_path}")
                print(f"🔥 多线程处理优势: 并发向量化，大幅提升处理速度")
                return True
            else:
                print(f"❌ 步骤2失败: 没有成功向量化的新闻")
                return False

        except Exception as e:
            print(f"❌ 步骤2异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def step3_generate_articles(self) -> bool:
        """步骤3: 话题合并与文章生成（多线程并发处理）"""
        print(f"\n✍️ 步骤3: 话题合并与文章生成")
        print("="*60)

        try:
            # 创建话题合并器，并指定向量数据库路径
            merger = DatabaseTopicMerger()

            # 手动设置向量数据库路径
            from vector_database import VectorDatabase
            merger.vector_db = VectorDatabase(self.vector_db_path)

            # 检查向量数据库是否有数据
            stats = merger.vector_db.stats
            if stats['total_documents'] == 0:
                print(f"❌ 步骤3失败: 向量数据库中没有数据")
                return False

            print(f"📊 向量数据库状态: {stats['total_documents']} 条文档")
            print(f"🚀 启动多线程并发处理...")
            print(f"⚙️ 并发配置: {merger.max_workers} 个线程同时处理")
            print(f"⚙️ API池配置: {len(merger.llm_config['api_keys'])} 个API Key并发调用")

            # 执行完整的话题合并和文章生成流程
            print(f"� 开始话题合并与文章生成...")

            # 步骤3.1: 提取新闻标题
            all_titles = merger.extract_all_news_titles()
            if not all_titles:
                print(f"❌ 步骤3失败: 无法提取新闻标题")
                return False

            print(f"📋 提取到 {len(all_titles)} 个新闻标题")

            # 步骤3.2: 智能合并话题（多线程并发）
            merged_topics = merger.merge_database_topics(all_titles)
            if not merged_topics:
                print(f"❌ 步骤3失败: 话题合并失败")
                return False

            print(f"🔄 第一次合并: {len(all_titles)} → {len(merged_topics)} 个话题")

            # 步骤3.3: 最终合并（多线程并发）
            final_topics = merger.final_merge_topics(merged_topics)
            if not final_topics:
                print(f"❌ 步骤3失败: 最终合并失败")
                return False

            print(f"🎯 最终合并: {len(merged_topics)} → {len(final_topics)} 个话题")

            # 步骤3.4: 生成文章（多线程并发）
            articles = merger.generate_all_articles(final_topics)

            if not articles:
                print(f"❌ 步骤3失败: 没有生成任何文章")
                return False

            # 步骤3.5: 文章已在generate_all_articles中保存到数据库
            print(f"💾 文章已保存到数据库...")

            print(f"✅ 步骤3完成: 话题合并与文章生成完成")
            print(f"📊 最终统计: {len(final_topics)} 个话题, {len(articles)} 篇文章")
            print(f"🔥 并发处理优势: 多线程+多API Key，大幅提升处理速度")
            return True

        except Exception as e:
            print(f"❌ 步骤3异常: {e}")
            import traceback
            traceback.print_exc()
            return False



    def run_pipeline(self) -> bool:
        """运行完整流水线"""
        print(f"🎯 开始执行完整的新闻处理流水线...")
        
        success_steps = 0
        total_steps = 3
        
        # 步骤1: 爬取新闻
        step1_success, news_data, news_count = self.step1_crawl_news()
        if step1_success:
            success_steps += 1
        else:
            self.print_final_summary(success_steps, total_steps, False)
            return False

        # 步骤2: 向量化
        step2_success = self.step2_vectorize_news(news_data)
        if step2_success:
            success_steps += 1
        else:
            self.print_final_summary(success_steps, total_steps, False)
            return False
        
        # 步骤3: 生成文章
        step3_success = self.step3_generate_articles()
        if step3_success:
            success_steps += 1
        
        # 打印最终总结
        pipeline_success = success_steps == total_steps
        self.print_final_summary(success_steps, total_steps, pipeline_success)
        
        return pipeline_success
    
    def print_final_summary(self, success_steps: int, total_steps: int, pipeline_success: bool):
        """打印最终总结"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print(f"\n" + "="*80)
        print(f"🎉 自动化新闻处理流水线总结")
        print(f"="*80)
        print(f"📅 处理日期: {self.today}")
        print(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️ 总耗时: {duration.total_seconds():.1f} 秒")
        print(f"")
        print(f"📊 执行结果:")
        print(f"   ✅ 成功步骤: {success_steps}/{total_steps}")
        print(f"   🎯 流水线状态: {'🎉 完全成功' if pipeline_success else '❌ 部分失败'}")

        if pipeline_success:
            print(f"")
            print(f"🔥 并行处理优势总结:")
            print(f"   📰 步骤1: 双线程并行爬取（网易+新浪同时进行）")
            print(f"   🔄 步骤2: 多线程向量化处理（3线程并发+分批优化API调用）")
            print(f"   ✍️ 步骤3: 多线程并发生成（话题合并+文章创作）")
            print(f"")
            print(f"📁 生成文件:")
            if os.path.exists(self.vector_db_path):
                print(f"   🔢 向量数据库: {self.vector_db_path}/ (仅保留向量数据)")
            print(f"   💾 文章数据: 已保存到MySQL数据库")
            print(f"   🚫 新闻CSV: 无需保存，直接处理内存数据")
        
        print(f"="*80)

    def cleanup_old_files(self, keep_days: int = 7):
        """清理旧文件（可选功能）"""
        print(f"\n🧹 清理 {keep_days} 天前的旧文件...")

        try:
            import glob
            from datetime import datetime, timedelta

            cutoff_date = datetime.now() - timedelta(days=keep_days)
            cutoff_str = cutoff_date.strftime("%Y%m%d")

            # 清理旧的CSV文件
            old_csv_files = glob.glob("news_unified_*.csv")
            for file in old_csv_files:
                try:
                    # 从文件名提取日期
                    date_part = file.split('_')[2][:8]  # news_unified_20250629_...
                    if date_part < cutoff_str:
                        os.remove(file)
                        print(f"   🗑️ 删除旧CSV: {file}")
                except:
                    pass

            # 清理旧的向量数据库
            old_vector_dirs = glob.glob("news_vectors_*")
            for dir_path in old_vector_dirs:
                try:
                    date_part = dir_path.split('_')[2]  # news_vectors_20250629
                    if date_part < cutoff_str:
                        shutil.rmtree(dir_path)
                        print(f"   🗑️ 删除旧向量库: {dir_path}")
                except:
                    pass

            # 清理旧的文章文件夹
            old_article_dirs = glob.glob("生成文章_*")
            for dir_path in old_article_dirs:
                try:
                    date_part = dir_path.split('_')[1][:8]  # 生成文章_20250629_...
                    if date_part < cutoff_str:
                        shutil.rmtree(dir_path)
                        print(f"   🗑️ 删除旧文章: {dir_path}")
                except:
                    pass

            print(f"✅ 清理完成")

        except Exception as e:
            print(f"⚠️ 清理过程中出现错误: {e}")


def print_usage():
    """打印使用说明"""
    print(f"""
🚀 自动化新闻处理流水线 - 使用说明
{"="*60}

功能：
  1. 爬取网易+新浪新闻 → 生成CSV文件
  2. CSV数据向量化 → 存储到向量数据库
  3. 话题智能合并 → 生成爆款文章 → 发送到映像笔记

使用方法：
  python automated_news_pipeline.py [新闻数量] [是否获取详细内容]

参数说明：
  新闻数量：
    - 数字：每个来源爬取的新闻数量（如：30, 50, 100）
    - all/a/全部：获取所有可用新闻
    - 默认：30

  是否获取详细内容：
    - true/1/yes/y：获取新闻详细内容（推荐）
    - false/0/no/n：只获取标题和摘要
    - 默认：true

使用示例：
  python automated_news_pipeline.py                    # 默认：30条，包含详细内容
  python automated_news_pipeline.py 50                 # 50条，包含详细内容
  python automated_news_pipeline.py 100 false          # 100条，不包含详细内容
  python automated_news_pipeline.py all true           # 全部新闻，包含详细内容

特殊命令：
  python automated_news_pipeline.py help               # 显示此帮助信息
  python automated_news_pipeline.py test               # 运行测试模式（少量数据）

注意事项：
  - 首次运行需要确保.env文件配置正确
  - 建议在网络良好的环境下运行
  - 完整流程可能需要10-30分钟，请耐心等待
  - 生成的文章会自动发送到映像笔记（如已配置）

{"="*60}
""")


def main():
    """主函数"""
    # 检查特殊命令
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['help', 'h', '-h', '--help', '帮助']:
            print_usage()
            return
        elif sys.argv[1].lower() in ['test', 't', '测试']:
            print("🧪 运行测试模式（少量数据）...")
            max_news_per_source = 5
            get_detail = False
            get_all = False
        elif sys.argv[1].lower() in ['all', 'a', '全部']:
            get_all = True
            max_news_per_source = 999
            get_detail = True
        else:
            try:
                max_news_per_source = int(sys.argv[1])
                get_detail = True
                get_all = False
            except ValueError:
                print("❌ 错误：新闻数量必须是数字，或使用 'all' 获取全部新闻")
                print("💡 使用 'python automated_news_pipeline.py help' 查看详细说明")
                return
    else:
        # 默认参数
        max_news_per_source = 30
        get_detail = True
        get_all = False

    # 解析第二个参数
    if len(sys.argv) > 2:
        get_detail = sys.argv[2].lower() in ['true', '1', 'yes', 'y']

    # 创建并运行流水线
    pipeline = AutomatedNewsPipeline(
        max_news_per_source=max_news_per_source,
        get_detail=get_detail,
        get_all=get_all
    )

    success = pipeline.run_pipeline()

    if success:
        print(f"\n🎊 恭喜！自动化新闻处理流水线执行成功！")
        print(f"💡 下次使用：python automated_news_pipeline.py help 查看更多选项")
    else:
        print(f"\n😞 流水线执行失败，请检查错误信息")
        print(f"💡 获取帮助：python automated_news_pipeline.py help")

    return success


if __name__ == "__main__":
    main()
