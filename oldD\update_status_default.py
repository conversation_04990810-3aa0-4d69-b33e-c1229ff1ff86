#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新articles表的status字段默认值
"""

import mysql.connector
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from config import get_database_config
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def update_status_default():
    """更新status字段的默认值"""
    try:
        # 获取数据库配置
        db_config = get_database_config()
        
        # 建立数据库连接
        connection = mysql.connector.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database'],
            charset=db_config['charset'],
            ssl_disabled=False if db_config['ssl_mode'] == 'REQUIRED' else True
        )
        
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        print("🔄 开始修改articles表的status字段默认值...")
        
        # 使用数据库
        cursor.execute("USE defaultdb")
        
        # 修改status字段的默认值
        alter_sql = """
        ALTER TABLE articles 
        MODIFY COLUMN status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '文章状态'
        """
        
        cursor.execute(alter_sql)
        connection.commit()
        
        print("✅ 成功修改articles表的status字段默认值为'draft'")
        
        # 验证修改结果
        cursor.execute("DESCRIBE articles")
        columns = cursor.fetchall()
        
        for column in columns:
            if column[0] == 'status':
                print(f"📊 status字段信息: {column}")
                break
        
        cursor.close()
        connection.close()
        print("✅ 数据库连接已关闭")
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库操作失败: {e}")
    except Exception as e:
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    update_status_default()
