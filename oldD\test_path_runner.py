#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径问题的简化版本
"""

import os
import sys
import subprocess

def test_path_execution():
    """测试路径执行"""
    print("🧪 测试路径执行问题")
    print("=" * 50)
    
    # 获取当前目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"📁 脚本目录: {script_dir}")
    print(f"📁 当前工作目录: {os.getcwd()}")
    
    # 切换到脚本目录
    os.chdir(script_dir)
    print(f"📁 切换后工作目录: {os.getcwd()}")
    
    # 检查文件是否存在
    script_path = os.path.join(script_dir, "automated_news_pipeline.py")
    print(f"📄 脚本路径: {script_path}")
    print(f"📄 文件存在: {os.path.exists(script_path)}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = script_dir
    print(f"🔧 PYTHONPATH: {env['PYTHONPATH']}")
    
    # 测试命令
    cmd = [sys.executable, script_path, "test"]
    print(f"📝 执行命令: {' '.join(cmd)}")
    
    try:
        # 执行命令（只运行5秒测试）
        print("🚀 开始执行...")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env,
            cwd=script_dir
        )
        
        # 等待5秒或进程结束
        try:
            stdout, stderr = process.communicate(timeout=5)
            print("✅ 进程正常结束")
            print(f"📤 输出: {stdout[:200]}...")
            if stderr:
                print(f"⚠️ 错误: {stderr[:200]}...")
        except subprocess.TimeoutExpired:
            print("⏰ 5秒超时，进程正在运行中（这是好现象）")
            process.terminate()
            stdout, stderr = process.communicate()
            print(f"📤 部分输出: {stdout[:200]}...")
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    test_path_execution()
