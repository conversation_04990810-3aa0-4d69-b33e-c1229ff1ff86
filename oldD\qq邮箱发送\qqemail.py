import smtplib
from email.mime.text import MIMEText
from email.header import Header
from email.utils import parseaddr, formataddr

# 格式化邮件地址
def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))

# 邮件配置信息
from_addr = '<EMAIL>'                 # 发件人地址
password = 'yuafiecxiggrdhib'                   # QQ邮箱授权码（非密码）
to_addr = '<EMAIL>'                        # 收件人地址
smtp_server = 'smtp.qq.com'                     # QQ邮箱SMTP服务器
smtp_port = 465                                 # SSL端口

# 构建邮件内容
subject = '你好，这是一封来自 Python 的邮件'
body = '你好，这是一封测试邮件，来自*****************，通过 Python 自动发送。'

message = MIMEText(body, 'plain', 'utf-8')
message['From'] = _format_addr(f'Python脚本 <{from_addr}>')
message['To'] = _format_addr(f'收件人 <{to_addr}>')
message['Subject'] = Header(subject, 'utf-8')

# 发送邮件
try:
    server = smtplib.SMTP_SSL(smtp_server, smtp_port)
    server.login(from_addr, password)
    server.sendmail(from_addr, [to_addr], message.as_string())
    server.quit()
    print('邮件发送成功')
except Exception as e:
    print('邮件发送失败:', e)