#!/bin/bash

# API Key测试脚本
# 测试6个API Key是否可用，使用DeepSeek-V3-Fast模型

# API Keys数组
API_KEYS=(
    "JHEsJv1aSv06NTy06K3VsBshU83C6E0XDtA1Yx9rVgKUYKqPgzfo2n8cfcZ_A7vI2BwsYc0Ai0dAI-woPJwtZg"
    "r0283fxCH9SB7UUKqgZq2wybCdND3MEQ8m4ZX9vdoxeYexDL24KICZxtN7gcSRxT24GtXdfF13WHJI2CqbWqLQ"
    "-qRxLu1kcIbuOHMEprPRCvoOmwUCMB2SVosUnchntfHffHHIDa_zBtLOR6tc8pAfdw_n6thuJO1jG21bkDjIAA"
    "aSHiOk6FguzVA4th6-GFgZ59P_SNE5kl24b7m9L861MOdw4BYeFnVxiqrEiUnJ4o46AYrg2OUIdlUn05cPtFqg"
    "Ow_rBGPwNCSkBXT4e0geAmaXRLMhV09vD2P2DIeFT3behy4bD4PPXuei-YyOFJjzgGPod_OIIjlfaPCg0wji7w"
    "fD8zQMDjF7bJy2cTe257T-vJlpfjFO0E_QcO74lIvrdTWSNzcy2Wjc2u2lSCasOEYj1c7uRjRZJn4cWE1wTRgg"
)

# API配置
API_URL="https://www.sophnet.com/api/open-apis/chat/completions"
MODEL="DeepSeek-V3-Fast"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "🚀 开始测试API Keys..."
echo "📡 API地址: $API_URL"
echo "🤖 模型: $MODEL"
echo "🔑 测试 ${#API_KEYS[@]} 个API Key"
echo "=" * 60

# 统计变量
success_count=0
failed_count=0
failed_keys=()

# 测试每个API Key
for i in "${!API_KEYS[@]}"; do
    key_num=$((i + 1))
    api_key="${API_KEYS[$i]}"
    
    echo -e "\n${BLUE}🧪 测试API Key #${key_num}${NC}"
    echo "🔑 Key: ${api_key:0:20}...${api_key: -10}"
    
    # 构建请求数据
    request_data='{
        "model": "'$MODEL'",
        "messages": [
            {
                "role": "user",
                "content": "你好，这是API Key测试，请简单回复确认。"
            }
        ],
        "max_tokens": 50,
        "temperature": 0.3
    }'
    
    # 发送请求
    response=$(curl -s -w "\n%{http_code}" \
        --location \
        --request POST "$API_URL" \
        --header "Authorization: Bearer $api_key" \
        --header "Content-Type: application/json" \
        --data-raw "$request_data" \
        --connect-timeout 10 \
        --max-time 30)
    
    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    # 检查结果
    if [ "$http_code" = "200" ]; then
        # 检查响应是否包含正确的结构
        if echo "$response_body" | grep -q '"choices"' && echo "$response_body" | grep -q '"content"'; then
            echo -e "   ${GREEN}✅ API Key #${key_num} 测试成功${NC}"
            echo "   📊 状态码: $http_code"
            
            # 提取回复内容
            content=$(echo "$response_body" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data['choices'][0]['message']['content'][:100] + '...' if len(data['choices'][0]['message']['content']) > 100 else data['choices'][0]['message']['content'])
except:
    print('无法解析回复内容')
" 2>/dev/null)
            echo "   💬 回复: $content"
            
            success_count=$((success_count + 1))
        else
            echo -e "   ${YELLOW}⚠️ API Key #${key_num} 响应格式异常${NC}"
            echo "   📊 状态码: $http_code"
            echo "   📄 响应: ${response_body:0:200}..."
            failed_count=$((failed_count + 1))
            failed_keys+=("Key #${key_num}")
        fi
    else
        echo -e "   ${RED}❌ API Key #${key_num} 测试失败${NC}"
        echo "   📊 状态码: $http_code"
        
        # 尝试解析错误信息
        if echo "$response_body" | grep -q "余额不足"; then
            echo "   💰 错误: 余额不足，需要充值"
        elif echo "$response_body" | grep -q "Invalid"; then
            echo "   🔑 错误: API Key无效"
        elif echo "$response_body" | grep -q "rate"; then
            echo "   ⏱️ 错误: 请求频率限制"
        else
            echo "   📄 错误详情: ${response_body:0:200}..."
        fi
        
        failed_count=$((failed_count + 1))
        failed_keys+=("Key #${key_num}")
    fi
    
    # 添加延迟避免频率限制
    sleep 1
done

# 输出测试结果总结
echo -e "\n" + "=" * 60
echo -e "${BLUE}📊 测试结果总结${NC}"
echo "=" * 60
echo -e "✅ 成功: ${GREEN}${success_count}${NC} 个API Key"
echo -e "❌ 失败: ${RED}${failed_count}${NC} 个API Key"

if [ ${#failed_keys[@]} -gt 0 ]; then
    echo -e "\n${RED}失败的API Keys:${NC}"
    for key in "${failed_keys[@]}"; do
        echo "   - $key"
    done
fi

if [ $success_count -gt 0 ]; then
    echo -e "\n${GREEN}🎉 有 $success_count 个API Key可用，可以更新到.env文件中！${NC}"
    echo -e "${YELLOW}💡 建议: 将可用的API Key按顺序配置为 LLM_API_KEY_1, LLM_API_KEY_2 等${NC}"
else
    echo -e "\n${RED}⚠️ 所有API Key都不可用，请检查Key是否正确或是否有余额${NC}"
fi

echo -e "\n🏁 测试完成！"
