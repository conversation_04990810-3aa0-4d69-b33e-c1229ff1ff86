#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API key显示功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database_topic_merger import DatabaseTopicMerger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_api_key_display():
    """测试API key显示功能"""
    print("🧪 测试API Key显示功能")
    print("=" * 50)
    
    try:
        # 创建话题合并器
        merger = DatabaseTopicMerger()
        
        # 测试几个不同的线程ID
        test_threads = ["TEST-T01", "TEST-T02", "TEST-T03"]
        
        for thread_id in test_threads:
            print(f"\n🔄 测试线程 [{thread_id}]:")
            
            # 获取API key分配
            api_key, key_index = merger.get_thread_api_key(thread_id)
            print(f"   分配的API Key: #{key_index + 1} - {api_key[:20]}...")
            
            # 测试API调用（会显示详细的key信息）
            result = merger.call_llm("你好，这是一个测试。请简单回复'测试成功'。", thread_id)
            
            if result:
                print(f"   ✅ API调用成功: {result[:30]}...")
            else:
                print(f"   ❌ API调用失败")
        
        print("\n" + "=" * 50)
        print("📊 线程API Key分配总结:")
        for thread_id, (api_key, key_index) in merger.thread_api_keys.items():
            print(f"   线程 [{thread_id}]: API Key #{key_index + 1} - {api_key[:20]}...")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_key_display()
