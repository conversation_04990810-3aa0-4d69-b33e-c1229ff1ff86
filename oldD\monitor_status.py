#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控定时任务执行状态
"""

import os
import json
import sys
from datetime import datetime, timedelta

def check_execution_status():
    """检查最近的执行状态"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    status_file = os.path.join(script_dir, "last_execution_status.json")
    
    print("🔍 检查定时任务执行状态")
    print("=" * 50)
    
    if not os.path.exists(status_file):
        print("❌ 未找到执行状态文件")
        print("💡 可能还没有执行过定时任务")
        return
    
    try:
        with open(status_file, 'r', encoding='utf-8') as f:
            status_data = json.load(f)
        
        timestamp_str = status_data.get('timestamp', '')
        success = status_data.get('success', False)
        log_file = status_data.get('log_file', '')
        
        # 解析时间
        try:
            last_run = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            time_diff = datetime.now() - last_run.replace(tzinfo=None)
        except:
            last_run = None
            time_diff = None
        
        print(f"📅 最后执行时间: {timestamp_str}")
        if time_diff:
            print(f"⏰ 距离现在: {time_diff}")
        print(f"📊 执行结果: {'✅ 成功' if success else '❌ 失败'}")
        print(f"📝 日志文件: {log_file}")
        
        # 检查是否超时未执行
        if time_diff and time_diff > timedelta(hours=7):
            print("⚠️ 警告: 超过7小时未执行，可能存在问题")
        
        # 显示最近的日志
        if log_file and os.path.exists(log_file):
            print("\n📋 最近日志内容 (最后20行):")
            print("-" * 30)
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines[-20:]:
                        print(line.rstrip())
            except Exception as e:
                print(f"❌ 读取日志失败: {e}")
        
    except Exception as e:
        print(f"❌ 读取状态文件失败: {e}")

def show_recent_logs():
    """显示最近的日志文件"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    log_dir = os.path.join(script_dir, "logs")
    
    print("\n📁 最近的日志文件:")
    print("=" * 50)
    
    if not os.path.exists(log_dir):
        print("❌ 日志目录不存在")
        return
    
    try:
        log_files = []
        for filename in os.listdir(log_dir):
            if filename.startswith("scheduled_news_") and filename.endswith(".log"):
                file_path = os.path.join(log_dir, filename)
                mtime = os.path.getmtime(file_path)
                log_files.append((filename, mtime))
        
        # 按时间排序
        log_files.sort(key=lambda x: x[1], reverse=True)
        
        for i, (filename, mtime) in enumerate(log_files[:5]):
            file_time = datetime.fromtimestamp(mtime)
            print(f"{i+1}. {filename} ({file_time.strftime('%Y-%m-%d %H:%M:%S')})")
        
        if not log_files:
            print("📝 暂无日志文件")
            
    except Exception as e:
        print(f"❌ 读取日志目录失败: {e}")

def show_cron_status():
    """显示cron任务状态"""
    print("\n🕐 Cron任务状态:")
    print("=" * 50)
    
    try:
        import subprocess
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        
        if result.returncode == 0:
            cron_lines = result.stdout.strip().split('\n')
            news_tasks = [line for line in cron_lines if 'scheduled_runner.py' in line]
            
            if news_tasks:
                print("✅ 找到新闻处理定时任务:")
                for task in news_tasks:
                    print(f"   {task}")
            else:
                print("❌ 未找到新闻处理定时任务")
                print("💡 请运行 bash setup_cron.sh 来设置定时任务")
        else:
            print("❌ 无法读取cron任务")
            
    except Exception as e:
        print(f"❌ 检查cron状态失败: {e}")

if __name__ == "__main__":
    check_execution_status()
    show_recent_logs()
    show_cron_status()
    
    print("\n🛠️ 管理命令:")
    print("   手动执行: python3 scheduled_runner.py")
    print("   查看实时日志: tail -f logs/cron.log")
    print("   设置定时任务: bash setup_cron.sh")
