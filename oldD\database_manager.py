"""
数据库管理模块
负责文章数据的存储和管理
"""

import mysql.connector
from mysql.connector import Error
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from config import get_database_config
except ImportError:
    print("❌ 找不到 config.py 文件")
    print("💡 请确保相关文件在当前目录")
    sys.exit(1)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库连接"""
        self.config = get_database_config()
        self.connection = None
        self.cursor = None
        
        # 检查配置
        required_keys = ["host", "user", "password", "database"]
        missing_keys = [key for key in required_keys if not self.config[key]]
        if missing_keys:
            raise ValueError(f"数据库配置缺少必要参数: {missing_keys}")
        
        self.connect()
        self.create_tables()
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config['charset'],
                ssl_disabled=False if self.config['ssl_mode'] == 'REQUIRED' else True,
                autocommit=True
            )
            
            if self.connection.is_connected():
                self.cursor = self.connection.cursor(dictionary=True)
                print(f"✅ 数据库连接成功: {self.config['user']}@{self.config['host']}:{self.config['port']}/{self.config['database']}")
                return True
            else:
                print("❌ 数据库连接失败")
                return False
                
        except Error as e:
            print(f"❌ 数据库连接错误: {e}")
            return False
    
    def create_tables(self):
        """创建必要的数据表"""
        try:
            # 创建文章表
            create_articles_table = """
            CREATE TABLE IF NOT EXISTS articles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                topic VARCHAR(500) NOT NULL COMMENT '文章话题',
                title VARCHAR(1000) DEFAULT NULL COMMENT '文章标题',
                content LONGTEXT NOT NULL COMMENT '文章内容',
                news_points LONGTEXT DEFAULT NULL COMMENT '新闻要点总结',
                word_count INT DEFAULT 0 COMMENT '文章字数',
                news_count INT DEFAULT 0 COMMENT '相关新闻数量',
                news_sources TEXT DEFAULT NULL COMMENT '新闻来源列表(JSON格式)',
                top_news_titles TEXT DEFAULT NULL COMMENT '主要新闻标题(JSON格式)',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '文章状态',
                INDEX idx_topic (topic(100)),
                INDEX idx_created_at (created_at),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI生成文章表'
            """
            
            # 创建话题统计表
            create_topics_table = """
            CREATE TABLE IF NOT EXISTS topics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                topic_name VARCHAR(500) NOT NULL COMMENT '话题名称',
                article_count INT DEFAULT 0 COMMENT '相关文章数量',
                total_news_count INT DEFAULT 0 COMMENT '总新闻数量',
                first_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次创建时间',
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                UNIQUE KEY uk_topic_name (topic_name(100)),
                INDEX idx_article_count (article_count),
                INDEX idx_first_created (first_created)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话题统计表'
            """
            
            # 创建处理日志表
            create_logs_table = """
            CREATE TABLE IF NOT EXISTS processing_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                batch_id VARCHAR(50) NOT NULL COMMENT '批次ID',
                total_news INT DEFAULT 0 COMMENT '总新闻数',
                total_topics INT DEFAULT 0 COMMENT '总话题数',
                total_articles INT DEFAULT 0 COMMENT '总文章数',
                compression_ratio VARCHAR(50) DEFAULT NULL COMMENT '压缩比例',
                processing_time_seconds INT DEFAULT 0 COMMENT '处理耗时(秒)',
                status ENUM('processing', 'completed', 'failed') DEFAULT 'processing' COMMENT '处理状态',
                error_message TEXT DEFAULT NULL COMMENT '错误信息',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                completed_at TIMESTAMP NULL DEFAULT NULL COMMENT '完成时间',
                INDEX idx_batch_id (batch_id),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处理日志表'
            """
            
            # 执行创建表语句
            self.cursor.execute(create_articles_table)
            self.cursor.execute(create_topics_table)
            self.cursor.execute(create_logs_table)
            
            print("✅ 数据表创建/检查完成")
            return True
            
        except Error as e:
            print(f"❌ 创建数据表失败: {e}")
            return False
    
    def save_article(self, article_data: Dict[str, Any]) -> Optional[int]:
        """保存单篇文章到数据库"""
        try:
            # 提取文章标题（从内容的第一行或话题）
            content = article_data.get('content', '')
            title = article_data.get('title', '')
            
            # 如果没有标题，尝试从内容中提取
            if not title and content:
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#') and len(line) > 10:
                        title = line[:100]  # 取前100个字符作为标题
                        break
                
                # 如果还是没有标题，使用话题
                if not title:
                    title = article_data.get('topic', '未命名文章')[:100]
            
            # 准备插入数据
            insert_query = """
            INSERT INTO articles (
                topic, title, content, news_points, word_count, 
                news_count, news_sources, top_news_titles
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            values = (
                article_data.get('topic', ''),
                title,
                content,
                article_data.get('news_points', ''),
                article_data.get('word_count', len(content)),
                article_data.get('news_count', 0),
                json.dumps(article_data.get('news_sources', []), ensure_ascii=False),
                json.dumps(article_data.get('top_news_titles', []), ensure_ascii=False)
            )
            
            self.cursor.execute(insert_query, values)
            article_id = self.cursor.lastrowid
            
            # 更新话题统计
            self.update_topic_stats(article_data.get('topic', ''), article_data.get('news_count', 0))
            
            print(f"✅ 文章保存成功 (ID: {article_id}): {title[:50]}...")
            return article_id
            
        except Error as e:
            print(f"❌ 保存文章失败: {e}")
            return None
    
    def save_articles_batch(self, articles: Dict[str, Dict], batch_info: Dict = None) -> Dict[str, Any]:
        """批量保存文章"""
        try:
            start_time = datetime.now()
            batch_id = start_time.strftime("%Y%m%d_%H%M%S")
            
            # 记录处理开始
            if batch_info:
                self.log_processing_start(batch_id, batch_info)
            
            saved_articles = []
            failed_articles = []
            
            print(f"🚀 开始批量保存 {len(articles)} 篇文章...")
            
            for i, (topic, article_info) in enumerate(articles.items(), 1):
                print(f"💾 [{i}/{len(articles)}] 保存文章: {topic[:50]}...")
                
                # 准备文章数据
                content = article_info.get('article', article_info.get('content', ''))
                word_count = article_info.get('word_count', 0)

                # 如果没有word_count或为0，则重新计算
                if word_count == 0 and content:
                    word_count = len(content)

                article_data = {
                    'topic': topic,
                    'content': content,
                    'news_points': article_info.get('news_points', ''),
                    'word_count': word_count,
                    'news_count': article_info.get('news_count', 0),
                    'news_sources': article_info.get('news_sources', []),
                    'top_news_titles': article_info.get('top_news_titles', article_info.get('top_news', []))
                }
                
                article_id = self.save_article(article_data)
                
                if article_id:
                    saved_articles.append({
                        'id': article_id,
                        'topic': topic,
                        'word_count': article_data['word_count']
                    })
                else:
                    failed_articles.append(topic)
            
            # 计算处理时间
            end_time = datetime.now()
            processing_time = int((end_time - start_time).total_seconds())
            
            # 更新处理日志
            if batch_info:
                self.log_processing_complete(batch_id, len(saved_articles), processing_time)
            
            result = {
                'batch_id': batch_id,
                'total_articles': len(articles),
                'saved_count': len(saved_articles),
                'failed_count': len(failed_articles),
                'saved_articles': saved_articles,
                'failed_articles': failed_articles,
                'processing_time_seconds': processing_time,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }
            
            print(f"✅ 批量保存完成: {len(saved_articles)}/{len(articles)} 篇文章保存成功")
            if failed_articles:
                print(f"❌ 保存失败的文章: {len(failed_articles)} 篇")
            
            return result
            
        except Exception as e:
            print(f"❌ 批量保存失败: {e}")
            return {
                'batch_id': batch_id if 'batch_id' in locals() else 'unknown',
                'total_articles': len(articles),
                'saved_count': 0,
                'failed_count': len(articles),
                'error': str(e)
            }

    def update_topic_stats(self, topic_name: str, news_count: int):
        """更新话题统计"""
        try:
            # 使用 ON DUPLICATE KEY UPDATE 语法
            update_query = """
            INSERT INTO topics (topic_name, article_count, total_news_count)
            VALUES (%s, 1, %s)
            ON DUPLICATE KEY UPDATE
                article_count = article_count + 1,
                total_news_count = total_news_count + VALUES(total_news_count),
                last_updated = CURRENT_TIMESTAMP
            """

            self.cursor.execute(update_query, (topic_name, news_count))

        except Error as e:
            print(f"❌ 更新话题统计失败: {e}")

    def log_processing_start(self, batch_id: str, batch_info: Dict):
        """记录处理开始"""
        try:
            insert_query = """
            INSERT INTO processing_logs (
                batch_id, total_news, total_topics, total_articles,
                compression_ratio, status
            ) VALUES (%s, %s, %s, %s, %s, %s)
            """

            values = (
                batch_id,
                batch_info.get('total_news', 0),
                batch_info.get('total_topics', 0),
                batch_info.get('total_articles', 0),
                batch_info.get('compression_ratio', ''),
                'processing'
            )

            self.cursor.execute(insert_query, values)

        except Error as e:
            print(f"❌ 记录处理开始失败: {e}")

    def log_processing_complete(self, batch_id: str, saved_count: int, processing_time: int):
        """记录处理完成"""
        try:
            update_query = """
            UPDATE processing_logs
            SET status = 'completed',
                total_articles = %s,
                processing_time_seconds = %s,
                completed_at = CURRENT_TIMESTAMP
            WHERE batch_id = %s
            """

            self.cursor.execute(update_query, (saved_count, processing_time, batch_id))

        except Error as e:
            print(f"❌ 记录处理完成失败: {e}")

    def get_articles(self, limit: int = 10, offset: int = 0, topic: str = None) -> List[Dict]:
        """获取文章列表"""
        try:
            base_query = """
            SELECT id, topic, title, word_count, news_count,
                   created_at, updated_at, status
            FROM articles
            """

            params = []
            if topic:
                base_query += " WHERE topic LIKE %s"
                params.append(f"%{topic}%")

            base_query += " ORDER BY created_at DESC LIMIT %s OFFSET %s"
            params.extend([limit, offset])

            self.cursor.execute(base_query, params)
            return self.cursor.fetchall()

        except Error as e:
            print(f"❌ 获取文章列表失败: {e}")
            return []

    def get_article_detail(self, article_id: int) -> Optional[Dict]:
        """获取文章详情"""
        try:
            query = """
            SELECT * FROM articles WHERE id = %s
            """

            self.cursor.execute(query, (article_id,))
            result = self.cursor.fetchone()

            if result:
                # 解析JSON字段
                if result.get('news_sources'):
                    try:
                        result['news_sources'] = json.loads(result['news_sources'])
                    except:
                        result['news_sources'] = []

                if result.get('top_news_titles'):
                    try:
                        result['top_news_titles'] = json.loads(result['top_news_titles'])
                    except:
                        result['top_news_titles'] = []

            return result

        except Error as e:
            print(f"❌ 获取文章详情失败: {e}")
            return None

    def get_topic_stats(self, limit: int = 20) -> List[Dict]:
        """获取话题统计"""
        try:
            query = """
            SELECT topic_name, article_count, total_news_count,
                   first_created, last_updated
            FROM topics
            ORDER BY article_count DESC, last_updated DESC
            LIMIT %s
            """

            self.cursor.execute(query, (limit,))
            return self.cursor.fetchall()

        except Error as e:
            print(f"❌ 获取话题统计失败: {e}")
            return []

    def get_processing_logs(self, limit: int = 10) -> List[Dict]:
        """获取处理日志"""
        try:
            query = """
            SELECT batch_id, total_news, total_topics, total_articles,
                   compression_ratio, processing_time_seconds, status,
                   created_at, completed_at
            FROM processing_logs
            ORDER BY created_at DESC
            LIMIT %s
            """

            self.cursor.execute(query, (limit,))
            return self.cursor.fetchall()

        except Error as e:
            print(f"❌ 获取处理日志失败: {e}")
            return []

    def close(self):
        """关闭数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection and self.connection.is_connected():
                self.connection.close()
                print("✅ 数据库连接已关闭")
        except Error as e:
            print(f"❌ 关闭数据库连接失败: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


def test_database_connection():
    """测试数据库连接"""
    try:
        with DatabaseManager() as db:
            print("🧪 测试数据库连接...")

            # 测试插入一条测试数据
            test_article = {
                'topic': '数据库连接测试',
                'content': '这是一条测试文章，用于验证数据库连接是否正常。',
                'news_points': '测试要点：数据库连接正常',
                'word_count': 25,
                'news_count': 1,
                'news_sources': ['测试来源'],
                'top_news_titles': ['测试新闻标题']
            }

            article_id = db.save_article(test_article)

            if article_id:
                print(f"✅ 数据库连接测试成功，测试文章ID: {article_id}")

                # 测试查询
                article = db.get_article_detail(article_id)
                if article:
                    print(f"✅ 数据查询测试成功: {article['title']}")

                return True
            else:
                print("❌ 数据库连接测试失败")
                return False

    except Exception as e:
        print(f"❌ 数据库连接测试异常: {e}")
        return False


if __name__ == "__main__":
    test_database_connection()
