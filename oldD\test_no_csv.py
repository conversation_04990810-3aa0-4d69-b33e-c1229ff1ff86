#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试无CSV文件的流程
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from 新闻爬取.unified_news_crawler import UnifiedNewsCrawler
    from csv_to_vector_processor import CSVNewsVectorProcessor
    from config import get_embedding_config
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_no_csv_flow():
    """测试无CSV文件的流程"""
    print("🧪 测试无CSV文件的新闻处理流程")
    print("=" * 50)
    
    # 步骤1: 测试爬虫返回数据
    print("\n📰 步骤1: 测试爬虫数据返回")
    try:
        crawler = UnifiedNewsCrawler(
            max_news_per_source=5,  # 少量数据测试
            get_detail=True,
            get_all=False
        )
        
        news_data, total_count = crawler.crawl_all()
        
        if news_data and total_count > 0:
            print(f"✅ 爬虫测试成功: 获得 {total_count} 条新闻数据")
            print(f"📊 数据类型: {type(news_data)}")
            print(f"📊 第一条数据示例: {list(news_data[0].keys()) if news_data else '无数据'}")
        else:
            print("❌ 爬虫测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 爬虫测试异常: {e}")
        return False
    
    # 步骤2: 测试向量化处理
    print("\n🔢 步骤2: 测试向量化处理")
    try:
        embedding_config = get_embedding_config()
        processor = CSVNewsVectorProcessor(embedding_config, "test_vectors")
        
        # 测试新的process_news_data方法
        processor.process_news_data(
            news_data=news_data[:3],  # 只处理前3条数据
            batch_size=2,
            max_news=3
        )
        
        print("✅ 向量化处理测试成功")
        
    except Exception as e:
        print(f"❌ 向量化处理测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n🎉 无CSV文件流程测试完成！")
    return True

if __name__ == "__main__":
    test_no_csv_flow()
